{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.1", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@reduxjs/toolkit": "^2.5.1", "@shopify/flash-list": "^1.7.6", "axios": "^1.9.0", "i": "^0.3.7", "moment": "^2.30.1", "npm": "^11.1.0", "react": "18.3.1", "react-native": "0.76.0", "react-native-calendar-timetable": "^1.0.7", "react-native-calendars": "1.1286.0", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "5.0.10", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.3", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "^2.23.1", "react-native-image-picker": "^8.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "4.4.0", "react-native-step-indicator": "^1.0.3", "react-native-svg": "^15.11.1", "react-native-svg-transformer": "^1.5.0", "react-native-toast-message": "^2.2.1", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.0-alpha.2", "@react-native-community/cli-platform-android": "15.0.0-alpha.2", "@react-native-community/cli-platform-ios": "15.0.0-alpha.2", "@react-native/babel-preset": "0.76.0", "@react-native/eslint-config": "0.76.0", "@react-native/metro-config": "0.76.0", "@react-native/typescript-config": "0.76.0", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-network-logger": "^2.0.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}