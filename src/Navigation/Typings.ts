import { NavigatorScreenParams } from "@react-navigation/native";
import { NativeStackScreenProps } from "@react-navigation/native-stack";

export type RootStackParams = {
  splash: undefined;
  authStack: NavigatorScreenParams<AuthStackParams>;
  setupStack: NavigatorScreenParams<SetUpStackParams>;
  mainStack: NavigatorScreenParams<MainStackParams>;
};

export type AuthStackParams = {
  LoginScreen: undefined;
  SignupScreen: undefined;
  PasswordReset: undefined;
  VerificationCode: {
    email: string;
    token: string;
    isFrom: "signup" | "reset";
  };
  SetNewPassword: {
    email: string;
    resetToken: string;
  };
};

export type MainStackParams = {
  bottomTabs: NavigatorScreenParams<BottomTabStackParams>;
  settings: undefined;
  businessDetails: undefined;
  serviceList: undefined;
  addNewService: {
    service: any;
  };
  addNewPackage: {
    packageToEdit?: any;
  };
  editPackage: {
    package: any;
  };
  manageCategories: undefined;
  addNewCategory: {
    categoryToEdit: { id: string; name: string; description: string } | null;
  };
  manageTeam: undefined;
  manageTeamMember: undefined;
  addTeamMember: {
    clientData?: any;
  };
  managePermissions: undefined;
  addPermissionRole: undefined;
  manageClient: undefined;
  addClient: undefined;
  TeamMeberDetail: { id: string };
  editProfile: undefined;
};

export type SetUpStackParams = {
  SetupBusiness: undefined;
  setupSteps: { isJoinExisting?: boolean };
};

export type BottomTabStackParams = {
  overView: undefined;
  manage: undefined;
};

export type SplashScreenProps = NativeStackScreenProps<
  RootStackParams,
  "splash"
>;
export type LoginScreenProps = NativeStackScreenProps<
  AuthStackParams & RootStackParams,
  "LoginScreen"
>;
export type SignUpScreenProps = NativeStackScreenProps<
  AuthStackParams & RootStackParams,
  "SignupScreen"
>;
export type VerificationCodeProps = NativeStackScreenProps<
  AuthStackParams & RootStackParams,
  "VerificationCode"
>;
export type ResetPasswordScreenProps = NativeStackScreenProps<
  AuthStackParams & RootStackParams,
  "PasswordReset"
>;
export type UpdatePasswordScreenProps = NativeStackScreenProps<
  AuthStackParams & RootStackParams,
  "SetNewPassword"
>;




// Setup Business Screens
export type SetupBusinessProps = NativeStackScreenProps<
  SetUpStackParams,
  "SetupBusiness"
>;
// Setup Business Screens
export type SetupStepsProps = NativeStackScreenProps<
  SetUpStackParams & RootStackParams,
  "setupSteps"
>;

// OverView Tab Screen
export type OverViewProps = NativeStackScreenProps<
  BottomTabStackParams & MainStackParams,
  "overView"
>;

// Manage Tab Screens
export type ManageProps = NativeStackScreenProps<
  BottomTabStackParams & MainStackParams,
  "manage"
>;

export type ManageTeamProps = NativeStackScreenProps<
  MainStackParams,
  "manageTeam"
>;

export type ManagePermissionProps = NativeStackScreenProps<
  MainStackParams,
  "managePermissions"
>;

export type ManageTeamMeberProps = NativeStackScreenProps<
  MainStackParams,
  "manageTeamMember"
>;

export type ServiceListProps = NativeStackScreenProps<
  MainStackParams,
  "serviceList"
>;

export type TeamMeberDetailProps = NativeStackScreenProps<
  MainStackParams,
  "TeamMeberDetail"
>;
export type SettingsProps = NativeStackScreenProps<
  MainStackParams & RootStackParams,
  "settings"
>;
