# Multilingual Support (i18n) Documentation

This document explains how to use the multilingual support system implemented in the GlamUp app.

## Overview

The app supports multiple languages using `react-i18next` library with:
- **English (en)** - Default language
- **Albanian (sq)** - Secondary language

## File Structure

```
src/Localization/
├── i18n.ts                    # i18n configuration
├── LanguageContext.tsx        # Language context and provider
├── useTranslation.ts          # Custom translation hook
├── languages/
│   ├── en.json               # English translations
│   └── sq.json               # Albanian translations
└── README.md                 # This documentation
```

## Components

### LanguageSelector Component
Located at `src/Components/LanguageSelector.tsx`
- Dropdown component for language selection
- Shows current language with flag
- Saves selection to AsyncStorage
- Integrates with theme system

## Usage

### 1. Basic Translation Usage

```tsx
import useTranslation from '../Localization/useTranslation';

const MyComponent = () => {
  const { common, auth, navigation } = useTranslation();
  
  return (
    <View>
      <Text>{common('save')}</Text>
      <Text>{auth('login')}</Text>
      <Text>{navigation('overview')}</Text>
    </View>
  );
};
```

### 2. Available Translation Categories

- `common` - Common UI elements (save, cancel, delete, etc.)
- `auth` - Authentication related (login, signup, password, etc.)
- `navigation` - Navigation labels (overview, calendar, clients, etc.)
- `appointments` - Appointment management
- `clients` - Client management
- `services` - Service management
- `team` - Team management
- `business` - Business setup and details
- `calendar` - Calendar specific terms
- `settings` - Settings screen
- `categories` - Category management
- `packages` - Package management
- `errors` - Error messages
- `validation` - Form validation messages
- `time` - Time-related terms

### 3. Using LanguageSelector Component

```tsx
import LanguageSelector from '../Components/LanguageSelector';

const SettingsScreen = () => {
  return (
    <View>
      <LanguageSelector 
        showLabel={true}
        style={customStyles.languageSelector}
      />
    </View>
  );
};
```

### 4. Translation with Parameters

```tsx
const { validation } = useTranslation();

// For translations with parameters like "Must be at least {{min}} characters"
const message = validation('minLength', { min: 6 });
```

## Adding New Languages

### 1. Create Language File
Create a new JSON file in `src/Localization/languages/` (e.g., `fr.json` for French)

### 2. Update i18n Configuration
Add the new language to `src/Localization/i18n.ts`:

```typescript
import fr from './languages/fr.json';

// Add to resources
resources: {
  en: { translation: en },
  sq: { translation: sq },
  fr: { translation: fr }, // Add new language
},
```

### 3. Update LanguageContext
Add the new language to `src/Localization/LanguageContext.tsx`:

```typescript
const availableLanguages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'sq', name: 'Albanian', nativeName: 'Shqip', flag: '🇦🇱' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' }, // Add new
];
```

## Adding New Translation Keys

### 1. Add to English File
Add the new key to `src/Localization/languages/en.json`:

```json
{
  "mySection": {
    "newKey": "New English Text"
  }
}
```

### 2. Add to All Other Language Files
Add the corresponding translation to all other language files.

### 3. Update useTranslation Hook (if new section)
If adding a new section, update `src/Localization/useTranslation.ts`:

```typescript
const mySection = (key: string, options?: any): string => {
  return t(`mySection.${key}`, options) as string;
};

return {
  // ... existing functions
  mySection,
};
```

## Best Practices

### 1. Key Naming Convention
- Use camelCase for keys
- Group related keys under sections
- Use descriptive names

```json
{
  "appointments": {
    "newAppointment": "New Appointment",
    "editAppointment": "Edit Appointment",
    "appointmentCreated": "Appointment created successfully"
  }
}
```

### 2. Avoid Hardcoded Strings
❌ Bad:
```tsx
<Text>Settings</Text>
```

✅ Good:
```tsx
<Text>{settings('settings')}</Text>
```

### 3. Use Parameters for Dynamic Content
❌ Bad:
```json
{
  "welcomeJohn": "Welcome John",
  "welcomeMary": "Welcome Mary"
}
```

✅ Good:
```json
{
  "welcome": "Welcome {{name}}"
}
```

```tsx
const message = common('welcome', { name: userName });
```

## Language Persistence

The selected language is automatically saved to AsyncStorage and restored when the app restarts.

## Testing Different Languages

1. Use the LanguageSelector component in Settings
2. Or programmatically change language:

```tsx
import { useLanguage } from '../Localization/LanguageContext';

const { changeLanguage } = useLanguage();
await changeLanguage('sq'); // Switch to Albanian
```

## Troubleshooting

### Missing Translation Keys
If a translation key is missing, the key itself will be displayed. Check the console for warnings in development mode.

### Language Not Changing
1. Ensure the language code exists in availableLanguages
2. Check that the language file is properly imported in i18n.ts
3. Verify AsyncStorage permissions

### Performance Considerations
- Translation files are loaded at app startup
- Language switching is instant after initial load
- Consider lazy loading for apps with many languages
