/**
 * Utility functions for currency handling
 */

/**
 * Map of currency codes to their symbols
 */
export const currencySymbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  CNY: '¥',
  INR: '₹',
  RUB: '₽',
  AUD: 'A$',
  CAD: 'C$',
  CHF: 'Fr',
  HKD: 'HK$',
  NZD: 'NZ$',
  SEK: 'kr',
  KRW: '₩',
  SGD: 'S$',
  NOK: 'kr',
  MXN: '$',
  BRL: 'R$',
  // Add more currencies as needed
};

/**
 * Get currency symbol from currency code
 * @param currencyCode The currency code (e.g., 'USD', 'EUR')
 * @returns The currency symbol or the original code if not found
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  if (!currencyCode) return '';
  return currencySymbols[currencyCode] || currencyCode;
};
