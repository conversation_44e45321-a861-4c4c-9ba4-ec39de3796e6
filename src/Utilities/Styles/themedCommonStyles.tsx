import { StyleSheet } from 'react-native';
import { textScale } from './responsiveSize';
import fontFamily from './fontFamily';
import { useTheme } from '../ThemeContext';

export const useThemedCommonStyles = () => {
  const { colors } = useTheme();
  
  return StyleSheet.create({
    font16400: {
      fontSize: textScale(16),
      fontWeight: '400',
      fontFamily: fontFamily.medium,
      color: colors.text,
    },
    font16Main: {
      fontSize: textScale(16),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font16medium: {
      fontSize: textScale(16),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.medium,
      lineHeight: 24,
    },
    font20main: {
      fontSize: textScale(20),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font20W400: {
      fontSize: textScale(20),
      fontWeight: '400',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font24W500: {
      fontSize: textScale(24),
      fontWeight: '500',
      color: colors.text,
      textAlign: 'center',
      fontFamily: fontFamily.regular,
    },
    font18W700Center: {
      fontSize: textScale(18),
      fontWeight: '600',
      color: colors.white,
      fontFamily: fontFamily.bold,
      alignSelf: 'center',
    },
    font18Main: {
      fontSize: textScale(18),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font18White: {
      fontSize: textScale(18),
      fontWeight: '600',
      color: colors.white,
      fontFamily: fontFamily.regular,
    },
    font14Center: {
      fontSize: textScale(14),
      fontWeight: '400',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font14greytext: {
      fontSize: textScale(14),
      fontWeight: '400',
      color: colors.greyText,
      fontFamily: fontFamily.regular,
    },
    font14: {
      fontSize: textScale(14),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font14Bold: {
      fontSize: textScale(14),
      fontFamily: fontFamily.regular,
      fontWeight: '700',
      color: colors.text,
    },
    font14Regular: {
      fontSize: textScale(14),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font14white: {
      fontSize: textScale(14),
      fontWeight: '500',
      color: colors.white,
      fontFamily: fontFamily.regular,
    },
    font12: {
      fontSize: textScale(12),
      fontWeight: '500',
      color: colors.greyText,
      fontFamily: fontFamily.regular,
    },
    font12white: {
      fontSize: textScale(12),
      fontWeight: '400',
      color: colors.white,
      fontFamily: fontFamily.regular,
    },
    font12main: {
      fontSize: textScale(12),
      fontWeight: '500',
      color: colors.text,
      fontFamily: fontFamily.regular,
    },
    font12Regular: {
      fontSize: textScale(12),
      fontWeight: '400',
      fontFamily: fontFamily.regular,
      color: colors.text,
    },
    font12SubText: {
      fontSize: textScale(12),
      fontWeight: '400',
      color: colors.greyText,
      fontFamily: fontFamily.regular,
    },
    Heading20font: {
      fontSize: textScale(20),
      fontWeight: '500',
      fontFamily: fontFamily.regular,
      color: colors.white,
      textAlign: 'center',
    },
    font12brown: {
      fontSize: textScale(12),
      fontWeight: '500',
      fontFamily: fontFamily.regular,
      color: colors.browntext,
    },
    font12Bold: {
      fontSize: textScale(12),
      fontWeight: '500',
      fontFamily: fontFamily.regular,
      color: colors.greyText,
    },
    font12Regualar2: {
      fontSize: textScale(12),
      fontWeight: '400',
      fontFamily: fontFamily.regular,
      color: colors.greyText,
    },
  });
};
