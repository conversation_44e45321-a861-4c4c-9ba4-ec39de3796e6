import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import { useTheme } from '../ThemeContext';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import { verticalScale } from '../Styles/responsiveSize';

interface ClientSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectClient: (clientId: string, clientName: string, phoneNumber: string) => void;
  clients?: any[];
  onAddNewClient?: () => void;
  selectedClient?: string | null;
}

const ClientSelectionModal: React.FC<ClientSelectionModalProps> = ({
  visible,
  onClose,
  onSelectClient,
  clients,
  onAddNewClient,
  selectedClient,
}) => {
  const { colors, isDarkMode } = useTheme();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode
                ? colors.cardBackground
                : colors.white,
            },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Select Client
            </Text>
            <TouchableOpacity onPress={onClose}>
              <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={clients || []}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.clientItem,
                  selectedClient === item.id && {
                    backgroundColor: isDarkMode
                      ? colors.bglight
                      : colors.bgsoft,
                  },
                ]}
                onPress={() => {
                  onSelectClient(item.id, item.name, item.number || '');
                  onClose();
                }}
              >
                {item.image && (
                  <Image
                    source={{ uri: item.image }}
                    style={styles.clientImage}
                  />
                )}
                <View style={styles.clientInfo}>
                  <Text style={[styles.clientName, { color: colors.text }]}>
                    {item.name}
                  </Text>
                  <Text
                    style={[styles.clientNumber, { color: colors.greyText }]}
                  >
                    {item.number || 'No phone number'}
                  </Text>
                </View>
                {selectedClient === item.id && (
                  <CustomIcon
                    Icon={ICONS.CheckRightIcon}
                    height={15}
                    width={15}
                  />
                )}
              </TouchableOpacity>
            )}
            ListFooterComponent={() => (
              <TouchableOpacity
                style={[
                  styles.addNewButton,
                  {
                    backgroundColor: isDarkMode
                      ? colors.primaryBase
                      : colors.primaryBase,
                  },
                ]}
                onPress={() => {
                  onClose();
                  if (onAddNewClient) {
                    onAddNewClient();
                  }
                }}
              >
                <CustomIcon Icon={ICONS.PlusIcon} height={15} width={15} />
                <Text style={styles.addNewText}>Enter Client Details Manually</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 10,
    padding: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
  },
  clientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
  },
  clientImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 14,
    fontWeight: '500',
  },
  clientNumber: {
    fontSize: 12,
    marginTop: verticalScale(2),
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  addNewText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default ClientSelectionModal;
