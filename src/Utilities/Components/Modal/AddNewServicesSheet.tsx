import React, { forwardRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
} from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import { useNavigation } from "@react-navigation/native";

import { verticalScale } from "../../Styles/responsiveSize";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ManageStackParams } from "../../../Navigation/Typings";
import { useThemedAddNewServicesSheetStyles } from "./themedAddNewServicesSheetStyles";
import { useTheme } from "../../ThemeContext";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";

type ServiceListParams = NativeStackNavigationProp<
  ManageStackParams,
  "serviceList"
>;

const AddNewServicesSheet = forwardRef((props, ref: any) => {
  const navigation = useNavigation<ServiceListParams>();
  const [pressedIndex, setPressedIndex] = useState(null); // Track pressed card
  const scaleAnims = [
    new Animated.Value(1), // For first card
    new Animated.Value(1), // For second card
    // Add more if you uncomment additional cards
  ];
  
  const { colors } = useTheme();
  const styles = useThemedAddNewServicesSheetStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const handlePressIn = (index: any) => {
    setPressedIndex(index);
    Animated.spring(scaleAnims[index], {
      toValue: 0.95, // Scale down slightly
      friction: 5,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = (index: any) => {
    setPressedIndex(null);
    Animated.spring(scaleAnims[index], {
      toValue: 1, // Scale back to original
      friction: 5,
      useNativeDriver: true,
    }).start();
  };

  const handleNavigate = (screen: any, index: any) => {
    ref?.current?.close();
    navigation.navigate(screen);
    handlePressOut(index); // Reset animation on navigate
  };

  return (
    <RBSheet
      ref={ref}
      openDuration={300}
      closeDuration={200}
      customStyles={{
        container: styles.sheetContainer,
      }}
    >
      <View style={styles.wrapper}>
        {/* Header */}
        <View style={styles.header}>
          <View />
          <Text style={styles.title}>Add new</Text>
          <TouchableOpacity
            onPress={() => ref.current.close()}
            style={styles.closeButton}
          >
            <CustomIcon Icon={ICONS.CrossIcon} height={12} width={12} />
          </TouchableOpacity>
        </View>

        {/* Options List */}
        {[
          {
            icon: ICONS.NewServicesIcon,
            label: "Service",
            screen: "addNewService",
            desc: "Add a new service to your list.",
          },
          {
            icon: ICONS.NewPackage,
            label: "Package",
            screen: "addNewPackage",
            desc: "Combine services like 'Haircut + Beard'",
          },
        ].map((item, index) => (
          <Animated.View
            key={index}
            style={[
              styles.optionCard,
              pressedIndex === index && styles.optionCardPressed, // Dynamic shadow
              { transform: [{ scale: scaleAnims[index] }] }, // Animation
            ]}
          >
            <TouchableOpacity
              activeOpacity={0.8}
              onPressIn={() => handlePressIn(index)}
              onPressOut={() => handlePressOut(index)}
              onPress={() => handleNavigate(item.screen, index)}
              style={styles.touchable}
            >
              <CustomIcon Icon={item.icon} height={48} width={48} />
              <View
                style={{
                  alignItems: "center",
                  marginTop: verticalScale(10),
                }}
              >
                <Text style={styles.optionText}>{item.label}</Text>
                <Text style={styles.optionDesc}>{item.desc}</Text>
              </View>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
    </RBSheet>
  );
});

export default AddNewServicesSheet;
