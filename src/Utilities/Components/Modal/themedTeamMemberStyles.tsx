import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
  height,
} from "../../Styles/responsiveSize";
import { useTheme } from "../../ThemeContext";

export const useThemedTeamMemberStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end",
    },
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
      backgroundColor: isDarkMode ? colors.black : colors.white,
      width: "100%",
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      padding: 20,
      maxHeight: height * 0.85,
      minHeight: height * 0.6,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 20,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "500",
      color: colors.text,
      textAlign: "center",
    },
    closeButton: {
      alignItems: "center",
      justifyContent: "center",
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      padding: Platform.OS === "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 10,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    selectAllContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      marginVertical: moderateScale(15),
    },
    memberItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 10,
      borderRadius: 10,
      marginBottom: 10,
      paddingHorizontal: moderateScale(12),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: isDarkMode ? "transparent" : colors.white,
      borderRadius: 4,
      marginRight: 8,
      alignItems: "center",
      justifyContent: "center",
    },
    profileImage: {
      width: 40,
      height: 40,
      marginRight: 10,
      borderRadius: 100,
    },
    memberInfo: {
      flex: 1,
    },
    memberName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    memberEmail: {
      fontSize: 12,
      fontWeight: "400",
      marginTop: verticalScale(5),
      color: colors.greyText,
    },
    flatListContainer: {
      flex: 1,
    },
    emptyText: {
      textAlign: "center",
      color: colors.text,
    },
  });
};
