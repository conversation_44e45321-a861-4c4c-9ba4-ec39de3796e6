import React from "react";
import {
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { ServiceListProps } from "../../../Navigation/Typings";
import { useTheme } from "../../ThemeContext";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useThemedItemOptionRbsheetStyles } from "./themedItemOptionRbsheetStyles";

interface ItemOptionRbsheetProps {
  rbSheetRef: React.RefObject<any>;
  item: any; // The service or package
  itemType: 'service' | 'package'; // Type of item
  navigation: ServiceListProps["navigation"];
  onEdit?: (item: any) => void; // Optional custom edit function
  onDelete?: (itemId: string) => void; // Optional custom delete function
}

const ItemOptionRbsheet = ({
  rbSheetRef,
  item,
  itemType,
  navigation,
  onEdit,
  onDelete,
}: ItemOptionRbsheetProps) => {
  const { colors } = useTheme();
  const styles = useThemedItemOptionRbsheetStyles();
  const themedCommonStyles = useThemedCommonStyles();
  
  const handleEdit = () => {
    rbSheetRef.current?.close();
    
    if (onEdit) {
      // Use custom edit function if provided
      onEdit(item);
    } else {
      // Default navigation based on item type
      if (itemType === 'service') {
        navigation.navigate("addNewService", { service: item });
      } else {
        navigation.navigate("addNewPackage", { packageToEdit: item });
      }
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      // Use custom delete function if provided
      onDelete(item.id);
    }
    rbSheetRef.current?.close();
  };

  return (
    <RBSheet
      ref={rbSheetRef}
      openDuration={250}
      customStyles={{
        container: styles.sheetContainer,
      }}
    >
      <View style={styles.headerContainer}>
        <View />
        <Text style={styles.headerTitle}>
          {itemType === 'service' ? 'Service' : 'Package'}
        </Text>
        <TouchableOpacity
          onPress={() => rbSheetRef.current?.close()}
          activeOpacity={0.8}
        >
          <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
        </TouchableOpacity>
      </View>
      <View>
        {[
          { 
            icon: ICONS.EditServices, 
            label: "Edit", 
            onPress: handleEdit,
            textColor: colors.text,
          },
          {
            icon: ICONS.Deleteservices,
            label: "Delete",
            textColor: colors.stateerrorbase,
            onPress: handleDelete,
          },
        ].map((option, index) => (
          <TouchableOpacity
            key={index}
            style={styles.optionCard}
            activeOpacity={0.8}
            onPress={option.onPress}
          >
            <CustomIcon Icon={option.icon} height={48} width={48} />
            <Text
              style={[
                option.textColor === colors.stateerrorbase ? styles.deleteText : styles.optionText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </RBSheet>
  );
};

export default ItemOptionRbsheet;
