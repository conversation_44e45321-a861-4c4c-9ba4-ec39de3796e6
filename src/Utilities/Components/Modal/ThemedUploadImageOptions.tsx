import React, { FC } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Modal from "react-native-modal";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "../../Styles/responsiveSize";
import { useTheme } from "../../ThemeContext";

type ThemedUploadImageOptionsProps = {
  isModalVisible: boolean;
  closeModal: () => void;
  onPressCamera: () => void;
  onPressGallery: () => void;
};

const ThemedUploadImageOptions: FC<ThemedUploadImageOptionsProps> = ({
  isModalVisible,
  closeModal,
  onPressCamera,
  onPressGallery,
}) => {
  const insets = useSafeAreaInsets();
  const { colors, isDarkMode } = useTheme();

  return (
    <Modal
      isVisible={isModalVisible}
      style={styles.modalContainer}
      onBackdropPress={closeModal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={300}
      animationOutTiming={300}
    >
      <View
        style={[
          styles.modalContent,
          { 
            paddingBottom: verticalScale(20) + insets.bottom,
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
          },
        ]}
      >
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            Select Image Upload Option
          </Text>
        </View>

        <View style={styles.optionsContainer}>
          <TouchableOpacity
            onPress={onPressGallery}
            style={[styles.optionButton, { backgroundColor: colors.primaryBase }]}
          >
            <Text style={styles.optionText}>Choose from Gallery</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            onPress={onPressCamera} 
            style={[styles.optionButton, { backgroundColor: colors.primaryBase }]}
          >
            <Text style={styles.optionText}>Upload from Camera</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            onPress={closeModal} 
            style={[
              styles.cancelButton, 
              { 
                borderColor: colors.border,
                backgroundColor: isDarkMode ? colors.bglight : 'transparent',
              }
            ]}
          >
            <Text style={[styles.cancelText, { color: colors.text }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default ThemedUploadImageOptions;

const styles = StyleSheet.create({
  modalContainer: {
    padding: 0,
    margin: 0,
    justifyContent: "flex-end",
  },
  modalContent: {
    paddingTop: verticalScale(20),
    paddingHorizontal: moderateScale(20),
    borderTopEndRadius: 15,
    borderTopStartRadius: 15,
    gap: verticalScale(25),
  },
  titleContainer: {
    marginBottom: verticalScale(10),
  },
  title: {
    fontSize: moderateScale(16),
    fontWeight: "600",
    textAlign: "center",
  },
  optionsContainer: {
    gap: verticalScale(15),
  },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: moderateScale(15),
    paddingVertical: verticalScale(14),
    borderRadius: 8,
  },
  optionText: {
    fontSize: moderateScale(15),
    color: "#FFFFFF",
    fontWeight: "500",
  },
  cancelButton: {
    paddingVertical: verticalScale(14),
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    borderWidth: 1,
  },
  cancelText: {
    fontSize: moderateScale(15),
    fontWeight: "500",
  },
});
