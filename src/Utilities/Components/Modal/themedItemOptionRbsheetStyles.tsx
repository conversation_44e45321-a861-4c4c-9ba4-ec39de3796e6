import { StyleSheet } from "react-native";
import { verticalScale } from "../../Styles/responsiveSize";
import { useTheme } from "../../ThemeContext";

export const useThemedItemOptionRbsheetStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    sheetContainer: {
      borderTopLeftRadius: 15,
      borderTopRightRadius: 15,
      padding: 20,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
      height: "auto",
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 10,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "500",
      color: colors.text,
    },
    optionCard: {
      backgroundColor: isDarkMode ? colors.bgblack : colors.bglight,
      borderRadius: 15,
      alignItems: "center",
      paddingTop: 10,
      marginVertical: verticalScale(10),
    },
    optionText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
      marginVertical: verticalScale(15),
    },
    deleteText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.stateerrorbase,
      marginVertical: verticalScale(15),
    },
  });
};
