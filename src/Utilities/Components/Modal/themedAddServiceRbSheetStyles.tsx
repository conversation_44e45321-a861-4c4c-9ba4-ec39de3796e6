import { StyleSheet } from "react-native";
import { useTheme } from "../../ThemeContext";

export const useThemedAddServiceRbSheetStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: { 
      flex: 1, 
      justifyContent: 'center', 
      alignItems: 'center' 
    },
    sheetContainer: {
      padding: 20,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    title: { 
      fontSize: 20,
      fontWeight: '500',
      color: colors.text,
    },
    tabsContainer: { 
      flexDirection: 'row', 
      marginBottom: 15 
    },
    activeTab: {
      backgroundColor: isDarkMode ? colors.bglight : colors.bglight,
      borderWidth: 1,
      borderColor: colors.primaryBase,
      padding: 8,
      borderRadius: 20,
      marginRight: 10,
    },
    inactiveTab: {
      backgroundColor: isDarkMode ? colors.bgsoft : colors.bgsoft,
      borderColor: isDarkMode ? colors.bgsoft : colors.bgsoft,
      padding: 8,
      borderRadius: 20,
      marginRight: 10,
    },
    categoryHeader: { 
      fontSize: 20,
      fontWeight: '500',
      color: colors.text,
      marginVertical: 10 
    },
    serviceItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: 10,
    },
    selectButton: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      height: 36,
      width: 36,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    selectedButton: { 
      backgroundColor: colors.primaryBase 
    },
    popularTag: {
      padding: 5,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.StateLightpinkColor,
      borderRadius: 20,
    },
    popularText: { 
      fontSize: 12,
      fontWeight: '400',
      color: colors.browntext 
    },
    newTag: {
      padding: 5,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.successSoft,
      borderRadius: 20,
    },
    newText: { 
      fontSize: 12,
      fontWeight: '400',
      color: colors.StateSuccessdark 
    },
    emptyText: {
      fontSize: 14,
      fontWeight: '400',
      textAlign: 'center',
      marginVertical: 20,
      color: colors.text,
    },
  });
};
