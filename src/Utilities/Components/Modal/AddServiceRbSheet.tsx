import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  FlatList,
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import CustomIcon from '../../../Components/CustomIcon';
import ICONS from '../../../Assets/Icon';
import { moderateScale } from '../../Styles/responsiveSize';
import { useAppSelector } from '../../../Redux/store';
import { selectServicesByCategory } from '../../../Redux/slices/servicesSlice';
import { getCurrencySymbol } from '../../currencyUtils';
import { useTheme } from '../../ThemeContext';
import { useThemedCommonStyles } from '../../Styles/themedCommonStyles';
import { useThemedAddServiceRbSheetStyles } from './themedAddServiceRbSheetStyles';

const AddServiceRbSheet = ({
  refRBSheet,
  selectedServices,
  setSelectedServices,
}: any) => {
  // Get services by category from Redux store
  const servicesByCategory = useAppSelector(selectServicesByCategory);
  const { categories } = useAppSelector((state) => state.services);
  const { colors } = useTheme();
  const styles = useThemedAddServiceRbSheetStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Debug logs
  console.log('Categories from Redux:', categories);
  console.log('Services by Category:', JSON.stringify(servicesByCategory));

  // Initialize selected category state
  const [selectedCategory, setSelectedCategory] = useState('');

  // Set initial category when categories are loaded
  useEffect(() => {
    if (categories && categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0]);
      console.log('Setting initial category to:', categories[0]);
    }
  }, [categories, selectedCategory]);

  // Track selected services with local state
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>(
    selectedServices ? selectedServices.map((service: any) => service.id) : []
  );

  // Toggle service selection
  const toggleServiceSelection = (serviceId: string) => {
    setSelectedServiceIds((prevSelectedIds) => {
      if (prevSelectedIds.includes(serviceId)) {
        return prevSelectedIds.filter((id) => id !== serviceId);
      } else {
        return [...prevSelectedIds, serviceId];
      }
    });
  };

  // Get services for the selected category
  const categoryData = servicesByCategory.find(
    (category) => category.category === selectedCategory
  );

  // Get services list with selection state
  const serviceList = categoryData
    ? categoryData.services.map((service) => ({
        ...service,
        selected: selectedServiceIds.includes(service.id),
      }))
    : [];

  // Debug log
  console.log('Selected Category:', selectedCategory);
  console.log('Service List:', serviceList.length);

  // Update parent component's selected services when selection changes
  useEffect(() => {
    if (setSelectedServices) {
      const allServices = servicesByCategory.flatMap(
        (category) => category.services
      );
      const selectedServicesData = allServices.filter((service) =>
        selectedServiceIds.includes(service.id)
      );
      setSelectedServices(selectedServicesData);
    }
  }, [selectedServiceIds, servicesByCategory, setSelectedServices]);

  return (
    <View style={styles.container}>
      <RBSheet
        ref={refRBSheet}
        height={Platform.OS === 'ios' ? 700 : 550}
        openDuration={300}
        closeDuration={200}
        customStyles={{ container: styles.sheetContainer }}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Add services to package</Text>
          <TouchableOpacity onPress={() => refRBSheet.current?.close()}>
            <CustomIcon
              Icon={ICONS.CrossIcon}
              width={12}
              height={12}
            />
          </TouchableOpacity>
        </View>

        {/* Category Tabs */}
        <View style={styles.tabsContainer}>
          {categories && categories.length > 0 ? (
            <FlatList
              showsHorizontalScrollIndicator={false}
              horizontal
              data={categories}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={
                    selectedCategory === item
                      ? styles.activeTab
                      : styles.inactiveTab
                  }
                  onPress={() => setSelectedCategory(item)}
                >
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '400',
                      color:
                        selectedCategory === item
                          ? colors.primaryBase
                          : colors.text,
                    }}
                  >
                    {item}
                  </Text>
                </TouchableOpacity>
              )}
            />
          ) : (
            <Text style={styles.emptyText}>No categories available</Text>
          )}
        </View>

        {/* Services List */}
        <View>
          <Text style={styles.categoryHeader}>{selectedCategory || 'No category selected'}</Text>
          {serviceList.length === 0 ? (
            <Text style={styles.emptyText}>
              {selectedCategory 
                ? 'No services in this category' 
                : 'Please select a category to view services'}
            </Text>
          ) : (
            <FlatList
              data={serviceList}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <View style={styles.serviceItem}>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: moderateScale(5),
                      }}
                    >
                      <Text style={themedCommonStyles.font14}>
                        {item.businessName}
                      </Text>
                    </View>
                    <Text style={themedCommonStyles.font14greytext}>
                      {getCurrencySymbol(item.currency)}
                      {item.price} · {item.duration}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.selectButton,
                      item.selected && styles.selectedButton,
                    ]}
                    onPress={() => toggleServiceSelection(item.id)}
                  >
                    {item.selected ? (
                      <CustomIcon
                        Icon={ICONS.CheckRightIcon}
                        width={15}
                        height={15}
                      />
                    ) : (
                      <Text style={themedCommonStyles.font20main}>+</Text>
                    )}
                  </TouchableOpacity>
                </View>
              )}
            />
          )}
        </View>
      </RBSheet>
    </View>
  );
};

export default AddServiceRbSheet;
