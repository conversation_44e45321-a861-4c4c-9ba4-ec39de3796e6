import React from "react";
import {
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import { useDispatch } from "react-redux";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { ServiceListProps } from "../../../Navigation/Typings";
import { deleteService } from "../../../Redux/slices/servicesSlice";
import { useTheme } from "../../ThemeContext";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useThemedServiceOptionRbsheetStyles } from "./themedServiceOptionRbsheetStyles";

interface ServiceOptionRbsheetProps {
  rbSheetRef: React.RefObject<any>;
  service: any; // Replace with Service type from servicesSlice
  navigation: ServiceListProps["navigation"];
}

const ServiceOptionRbsheet = ({
  rbSheetRef,
  service,
  navigation,
}: ServiceOptionRbsheetProps) => {
  const dispatch = useDispatch();
  const { colors } = useTheme();
  const styles = useThemedServiceOptionRbsheetStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const handleEdit = () => {
    rbSheetRef.current?.close();
    navigation.navigate("addNewService", { service }); // Pass service data for editing
  };

  const handleDelete = () => {
    dispatch(deleteService(service.id));
    rbSheetRef.current?.close();
  };

  return (
    <RBSheet
      ref={rbSheetRef}
      openDuration={250}
      customStyles={{
        container: styles.sheetContainer,
      }}
    >
      <View style={styles.headerContainer}>
        <View />
        <Text style={styles.headerTitle}>Service</Text>
        <TouchableOpacity
          onPress={() => rbSheetRef.current?.close()}
          activeOpacity={0.8}
        >
          <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
        </TouchableOpacity>
      </View>
      <View>
        {[
          { 
            icon: ICONS.EditServices, 
            label: "Edit", 
            onPress: handleEdit,
            textColor: colors.text,
          },
          // { icon: ICONS.DuplicateServices, label: 'Duplicate', onPress: () => {} }, // Placeholder for duplicate
          {
            icon: ICONS.Deleteservices,
            label: "Delete",
            textColor: colors.stateerrorbase,
            onPress: handleDelete,
          },
        ].map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.optionCard}
            activeOpacity={0.8}
            onPress={item.onPress}
          >
            <CustomIcon Icon={item.icon} height={48} width={48} />
            <Text
              style={[
                item.textColor === colors.stateerrorbase ? styles.deleteText : styles.optionText,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </RBSheet>
  );
};

export default ServiceOptionRbsheet;
