import { Platform, StyleSheet } from "react-native";
import { moderateScale, verticalScale } from "../../Styles/responsiveSize";
import { useTheme } from "../../ThemeContext";

export const useThemedAddNewServicesSheetStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    sheetContainer: {
      padding: moderateScale(15),
      borderTopLeftRadius: 15,
      borderTopRightRadius: 15,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      flex: 1,
    },
    wrapper: {
      width: "100%",
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: verticalScale(10),
    },
    title: { 
      fontSize: 20,
      fontWeight: '500',
      color: colors.text,
    },
    closeButton: {
      alignItems: "flex-end",
    },
    optionCard: {
      backgroundColor: isDarkMode ? colors.bglight : colors.bglight,
      borderRadius: 15,
      alignItems: "center",
      paddingVertical: 10,
      marginVertical: verticalScale(10),
    },
    optionCardPressed: {
      // Shadow when pressed
      ...Platform.select({
        ios: {
          shadowColor: isDarkMode ? "#fff" : "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: isDarkMode ? 0.2 : 0.3,
          shadowRadius: 6,
        },
        android: {
          elevation: 6,
        },
      }),
    },
    touchable: {
      width: "100%",
      alignItems: "center",
    },
    optionText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    optionDesc: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.greyText,
    },
  });
};
