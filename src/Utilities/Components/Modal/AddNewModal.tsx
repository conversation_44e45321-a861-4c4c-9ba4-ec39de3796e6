import React from "react";
import {
  FlatList,
  Modal,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import { useNavigation } from "@react-navigation/native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { SetIsAddNewVisible } from "../../../Redux/slices/modalSlice";
import { useAppDispatch, useAppSelector } from "../../../Redux/store";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useTheme } from "../../ThemeContext";

const AddNewModal = () => {
  const isAddNewVisible = useAppSelector(
    (state) => state.modals.isAddNewVisible
  );
  const navigation = useNavigation<any>();
  const dispatch = useAppDispatch();
  const { colors, isDarkMode } = useTheme();
  const themedCommonStyles = useThemedCommonStyles();

  const handleClose = () => dispatch(SetIsAddNewVisible(false));

  const options = [
    // {
    //   id: "1",
    //   icon: ICONS.AppointmentIcon,
    //   label: "Appointment",
    //   onCLick: () => {
    //     navigation.navigate("");
    //   },
    // },
    {
      id: "2",
      icon: ICONS.ClientIcon,
      label: "Client",
      onCLick: () => {
        dispatch(SetIsAddNewVisible(false));
        navigation.navigate("addClient");
      },
    },
    // {
    //   id: "3",
    //   icon: ICONS.BlockedTime,
    //   label: "Blocked time",
    //   onCLick: () => {
    //     navigation.navigate("");
    //   },
    // },
    {
      id: "4",
      icon: ICONS.TeamMember,
      label: "Team member",
      onCLick: () => {
        dispatch(SetIsAddNewVisible(false));
        navigation.navigate("addTeamMember", {
          clientData: null,
        });
      },
    },
  ];

  return (
    <Modal visible={isAddNewVisible} transparent animationType="slide">
      <TouchableOpacity
        activeOpacity={1}
        onPress={handleClose}
        style={{
          backgroundColor: "rgba(0,0,0,0.5)",
          flex: 1,
        }}
      />
      <View style={styles.container}>
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode ? colors.bgblack : colors.white,
            },
          ]}
        >
          <View style={styles.header}>
            <View />
            <Text
              style={[themedCommonStyles.font20main, { textAlign: "center" }]}
            >
              Add new
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={{ alignItems: "center", justifyContent: "center" }}
            >
              <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
            </TouchableOpacity>
          </View>
          <FlatList
            data={options}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            renderItem={({ item }) => (
              <Pressable
                onPress={item.onCLick}
                style={[
                  styles.optionContainer,
                  {
                    backgroundColor: isDarkMode
                      ? colors.cardBackground
                      : colors.bgsoft,
                  },
                ]}
              >
                <CustomIcon Icon={item.icon} height={48} width={48} />
                <Text
                  style={[
                    themedCommonStyles.font14,
                    { margin: 5, color: colors.text },
                  ]}
                >
                  {item.label}
                </Text>
              </Pressable>
            )}
          />
        </View>
      </View>
    </Modal>
  );
};

export default AddNewModal;

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "100%",
    padding: 20,
    position: "absolute",
    bottom: 0,
    borderTopRightRadius: 24,
    borderTopLeftRadius: 24,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  optionContainer: {
    padding: 20,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
    borderRadius: 8,
  },
});
