import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale } from '../../Styles/responsiveSize';
import { useTheme } from '../../ThemeContext';

export const useThemedCalendarAppointmentViewStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    overlay: {
      zIndex: 1,
      flex: 1,
    },
    loggingButton: {
      position: 'absolute',
      bottom: 10,
      right: moderateScale(20),
      zIndex: 3,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      backgroundColor: colors.greyText,
      padding: 10,
      borderRadius: 20,
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-end',
      gap: moderateScale(5),
    },
    loggingMenu: {
      position: 'absolute',
      bottom: Platform.OS === 'ios' ? verticalScale(10) : verticalScale(20),
      right: moderateScale(10),
      alignItems: 'flex-end',
      borderRadius: 12,
      padding: verticalScale(10),
      zIndex: 2,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
      paddingVertical: verticalScale(7),
      paddingHorizontal: moderateScale(5),
      borderRadius: 10,
      shadowColor: colors.greyText,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    menuText: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text,
    },
    iconWrapper: {
      width: 48,
      height: 48,
      borderRadius: 35,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.white,
    },
    notificationBadge: {
      position: 'absolute',
      top: 0,
      right: 0,
      backgroundColor: 'red',
      borderRadius: 100,
      height: 20,
      width: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    badgeText: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.white,
    },
  });
};
