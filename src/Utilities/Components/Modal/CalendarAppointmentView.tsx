import { BlurView } from "@react-native-community/blur";
import React, { Dispatch, FC, SetStateAction, useState } from "react";
import {
  Platform,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  useAnimatedStyle,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { useTheme } from "../../ThemeContext";
import { useThemedCalendarAppointmentViewStyles } from "./themedCalendarAppointmentViewStyles";

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

type AddLogButtonProps = {
  selectedOption: "Calendar" | "My Appointments";
  setSelectedOption: Dispatch<SetStateAction<"Calendar" | "My Appointments">>;
  upcomingAppointments: number;
};

const CalendarAppointmentView: FC<AddLogButtonProps> = ({
  selectedOption,
  setSelectedOption,
  upcomingAppointments,
}) => {
  const [isLoggingButtonOpen, setIsLoggingButtonOpen] = useState(false);
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedCalendarAppointmentViewStyles();

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    backgroundColor: withTiming(colors.greyText, { duration: 300 }),
  }));

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withTiming(isLoggingButtonOpen ? 1 : 0, { duration: 300 }),
  }));

  const menuAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: withSpring(isLoggingButtonOpen ? 0 : 10) }],
    opacity: withTiming(isLoggingButtonOpen ? 1 : 0, { duration: 200 }),
  }));

  return (
    <>
      {isLoggingButtonOpen && (
        <Animated.View
          style={[
            { position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 },
            styles.overlay,
            overlayAnimatedStyle,
          ]}
          pointerEvents={isLoggingButtonOpen ? "auto" : "none"}
        >
          <BlurView
            style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
            blurType={isDarkMode ? "dark" : "light"}
            blurAmount={20}
            reducedTransparencyFallbackColor="black"
          >
            <TouchableOpacity
              style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
              onPress={() => setIsLoggingButtonOpen(false)}
            />
          </BlurView>
        </Animated.View>
      )}

      {isLoggingButtonOpen && (
        <Animated.View style={[styles.loggingMenu, menuAnimatedStyle]}>
          {[
            {
              icon: ICONS.Appointment,
              activeIcon: ICONS.SelectedAppointment,
              label: "My Appointments",
            },
            {
              icon: ICONS.CalendarView,
              activeIcon: ICONS.SelectedCalenderView,
              label: "Calendar",
            },
          ].map(({ icon, label, activeIcon }) => {
            const isSelected = selectedOption === label;

            return (
              <TouchableOpacity
                key={label}
                activeOpacity={0.9}
                style={styles.menuItem}
                onPress={() => {
                  setSelectedOption(label as "Calendar" | "My Appointments");
                  setIsLoggingButtonOpen(false);
                }}
              >
                <Text style={styles.menuText}>
                  {label === "Calendar" ? "View Calendar" : label}
                </Text>
                <View
                  style={[
                    styles.iconWrapper,
                    {
                      backgroundColor: isSelected ? colors.black : colors.white,
                    },
                  ]}
                >
                  <CustomIcon
                    Icon={isSelected ? activeIcon : icon}
                    height={24}
                    width={24}
                  />
                </View>
                {label === "My Appointments" && (
                  <View style={styles.notificationBadge}>
                    <Text style={styles.badgeText}>
                      {upcomingAppointments}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </Animated.View>
      )}

      {!isLoggingButtonOpen && (
        <AnimatedTouchableOpacity
          activeOpacity={0.95}
          style={[styles.loggingButton, buttonAnimatedStyle]}
          onPress={() => setIsLoggingButtonOpen(true)}
        >
          <Text style={styles.buttonText}>
            {selectedOption}
          </Text>
          <CustomIcon Icon={ICONS.DownWhiteIcon} height={20} width={20} />
        </AnimatedTouchableOpacity>
      )}
    </>
  );
};

export default CalendarAppointmentView;
