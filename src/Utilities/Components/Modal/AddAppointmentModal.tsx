import moment from "moment";
import React, { useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import DatePicker from "react-native-date-picker";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { useAppDispatch, useAppSelector } from "../../../Redux/store";
import ClientSelectionModal from "../../Components/ClientSelectionModal";
import ServiceDropdown from "../../Components/ServiceDropdown";
import TeamMemberDropdown from "../../Components/TeamMemberDropdown";
import { moderateScale } from "../../Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useTheme } from "../../ThemeContext";

interface AddAppointmentModalProps {
  visible: boolean;
  onClose: () => void;
  clientId: string;
  clientName: string;
  startTime: string;
  date: string;
  onAddAppointment: (appointment: any) => void;
}

const AddAppointmentModal: React.FC<AddAppointmentModalProps> = ({
  visible,
  onClose,
  clientId,
  clientName,
  startTime,
  date,
  onAddAppointment,
}) => {
  const { colors, isDarkMode } = useTheme();
  const dispatch = useAppDispatch();

  // Calculate default end time (30 minutes after start time)
  const calculateDefaultEndTime = (start: string) => {
    const [hours, minutes] = start.split(":").map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    startDate.setMinutes(startDate.getMinutes() + 30);
    return `${String(startDate.getHours()).padStart(2, "0")}:${String(
      startDate.getMinutes()
    ).padStart(2, "0")}`;
  };

  // Get team members, services, and clients from Redux store
  const { members } = useAppSelector((state) => state.members);
  const { services } = useAppSelector((state) => state.services);
  const { clients } = useAppSelector((state) => state.clients);

  // State for form fields
  const [selectedClientId, setSelectedClientId] = useState<string | null>(
    clientId || null
  );
  const [title, setTitle] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [isNewClient, setIsNewClient] = useState(false);
  const [serviceId, setServiceId] = useState<string | null>(null);
  const [serviceName, setServiceName] = useState("");
  const [serviceDuration, setServiceDuration] = useState("");
  const [teamMemberId, setTeamMemberId] = useState<string | null>(null);
  const [teamMemberName, setTeamMemberName] = useState("");
  const [appointmentStartTime, setAppointmentStartTime] = useState(startTime);
  const [appointmentEndTime, setAppointmentEndTime] = useState(
    calculateDefaultEndTime(startTime)
  );
  const [status, setStatus] = useState("CONFIRMED");
  const [isStartTimePickerOpen, setIsStartTimePickerOpen] = useState(false);
  const [isEndTimePickerOpen, setIsEndTimePickerOpen] = useState(false);

  // Handle team member selection
  const handleSelectTeamMember = (id: string, name: string) => {
    setTeamMemberId(id);
    setTeamMemberName(name);
  };

  // Handle service selection
  const handleSelectService = (id: string, name: string, duration: string) => {
    setServiceId(id);
    setServiceName(name);
    setServiceDuration(duration);

    // Update end time based on service duration
    if (duration) {
      const durationMinutes = parseInt(duration.split(" ")[0]);
      if (!isNaN(durationMinutes)) {
        const [hours, minutes] = appointmentStartTime.split(":").map(Number);
        const startDate = new Date();
        startDate.setHours(hours, minutes, 0, 0);
        startDate.setMinutes(startDate.getMinutes() + durationMinutes);
        setAppointmentEndTime(
          `${String(startDate.getHours()).padStart(2, "0")}:${String(
            startDate.getMinutes()
          ).padStart(2, "0")}`
        );
      }
    }
  };

  // Convert string time to Date object for the time picker
  const timeStringToDate = (timeString: string) => {
    const [hours, minutes] = timeString.split(":").map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  // Handle client selection
  const handleSelectClient = (id: string, name: string, phone: string) => {
    setSelectedClientId(id);
    setTitle(name);
    setPhoneNumber(phone);
    setIsNewClient(false);
  };

  // State for client selection modal
  const [isClientModalVisible, setIsClientModalVisible] = useState(false);

  // Open client selection modal
  const openClientSelectionModal = () => {
    setIsClientModalVisible(true);
  };

  // Handle form submission
  const handleAddAppointment = () => {
    if (!title || !serviceId || !teamMemberId) {
      // Show validation error
      return;
    }

    // Validate client information
    if (isNewClient && !phoneNumber) {
      // Show validation error for new client
      return;
    }

    const newAppointment = {
      id: Date.now().toString(),
      clientId: selectedClientId,
      clientName: title,
      clientPhone: phoneNumber,
      startTime: appointmentStartTime,
      endTime: appointmentEndTime,
      title,
      status,
      date,
      service: serviceName,
      teamMember: teamMemberName,
      teamMemberId: teamMemberId,
    };

    onAddAppointment(newAppointment);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalContainer}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={() => {
            Keyboard.dismiss();
            onClose();
          }}
        />
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode
                ? colors.cardBackground
                : colors.white,
            },
          ]}
        >
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <View style={styles.header}>
              <View />
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                New Appointment
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            {/* Form Fields */}
            <View style={styles.formContainer}>
              {/* Client Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Client <Text style={styles.required}>*</Text>
                </Text>
                <View
                  style={[
                    styles.inputContainer,
                    {
                      backgroundColor: isDarkMode
                        ? colors.cardBackground
                        : colors.white,
                      borderColor: colors.border,
                      marginBottom: 10,
                    },
                  ]}
                >
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    value={title}
                    onChangeText={setTitle}
                    placeholder="Client name"
                    placeholderTextColor={colors.greyText}
                  />
                </View>
                <View
                  style={[
                    styles.inputContainer,
                    {
                      backgroundColor: isDarkMode
                        ? colors.cardBackground
                        : colors.white,
                      borderColor: colors.border,
                    },
                  ]}
                >
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    placeholder="Phone number"
                    placeholderTextColor={colors.greyText}
                    keyboardType="phone-pad"
                  />
                </View>
                <TouchableOpacity
                  style={styles.backToSelectionButton}
                  onPress={openClientSelectionModal}
                >
                  <Text style={{ color: colors.primaryBase }}>
                    Select from existing clients
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Client Selection Modal */}
              <ClientSelectionModal
                visible={isClientModalVisible}
                onClose={() => setIsClientModalVisible(false)}
                onSelectClient={handleSelectClient}
                clients={clients}
                selectedClient={selectedClientId}
                onAddNewClient={() => setIsClientModalVisible(false)}
              />

              {/* Service */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Service <Text style={styles.required}>*</Text>
                </Text>
                <ServiceDropdown
                  selectedService={serviceId}
                  onSelectService={handleSelectService}
                  services={services}
                />
              </View>

              {/* Team Member */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Team Member <Text style={styles.required}>*</Text>
                </Text>
                <TeamMemberDropdown
                  selectedMember={teamMemberId}
                  onSelectMember={handleSelectTeamMember}
                  teamMembers={members}
                />
              </View>

              {/* Time Selection */}
              <View style={styles.timeContainer}>
                {/* Start Time */}
                <View style={[styles.timeInput, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    Start Time <Text style={styles.required}>*</Text>
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.timeSelector,
                      {
                        backgroundColor: isDarkMode
                          ? colors.cardBackground
                          : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setIsStartTimePickerOpen(true)}
                  >
                    <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                    <Text style={[styles.timeText, { color: colors.text }]}>
                      {appointmentStartTime}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* End Time */}
                <View style={[styles.timeInput, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    End Time <Text style={styles.required}>*</Text>
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.timeSelector,
                      {
                        backgroundColor: isDarkMode
                          ? colors.cardBackground
                          : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setIsEndTimePickerOpen(true)}
                  >
                    <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                    <Text style={[styles.timeText, { color: colors.text }]}>
                      {appointmentEndTime}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Status Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Status
                </Text>
                <View style={styles.statusContainer}>
                  <TouchableOpacity
                    style={[
                      styles.statusOption,
                      status === "CONFIRMED" && {
                        backgroundColor: isDarkMode
                          ? colors.primaryBase
                          : colors.primaryBase,
                      },
                    ]}
                    onPress={() => setStatus("CONFIRMED")}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        status === "CONFIRMED" && { color: colors.white },
                        status !== "CONFIRMED" && { color: colors.text },
                      ]}
                    >
                      Confirmed
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusOption,
                      status === "PENDING" && {
                        backgroundColor: isDarkMode
                          ? colors.primaryBase
                          : colors.primaryBase,
                      },
                    ]}
                    onPress={() => setStatus("PENDING")}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        status === "PENDING" && { color: colors.white },
                        status !== "PENDING" && { color: colors.text },
                      ]}
                    >
                      Pending
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Add Button */}
              <TouchableOpacity
                style={[
                  styles.addButton,
                  {
                    backgroundColor: isDarkMode
                      ? colors.primaryBase
                      : colors.primaryBase,
                  },
                  (!title || !serviceId || !teamMemberId) && { opacity: 0.5 },
                ]}
                onPress={handleAddAppointment}
                disabled={!title || !serviceId || !teamMemberId}
              >
                <Text style={styles.addButtonText}>Add Appointment</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          {/* Time Pickers */}
          <DatePicker
            modal
            open={isStartTimePickerOpen}
            date={timeStringToDate(appointmentStartTime)}
            mode="time"
            theme={isDarkMode ? "dark" : "light"}
            onConfirm={(date) => {
              setIsStartTimePickerOpen(false);
              const formattedTime = moment(date).format("HH:mm");
              setAppointmentStartTime(formattedTime);

              // Update end time to be 30 minutes after start time
              setAppointmentEndTime(calculateDefaultEndTime(formattedTime));
            }}
            onCancel={() => setIsStartTimePickerOpen(false)}
          />

          <DatePicker
            modal
            open={isEndTimePickerOpen}
            date={timeStringToDate(appointmentEndTime)}
            mode="time"
            theme={isDarkMode ? "dark" : "light"}
            onConfirm={(date) => {
              setIsEndTimePickerOpen(false);
              setAppointmentEndTime(moment(date).format("HH:mm"));
            }}
            onCancel={() => setIsEndTimePickerOpen(false)}
          />
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "100%",
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    padding: 20,
    maxHeight: "80%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "500",
    textAlign: "center",
  },
  closeButton: {
    alignItems: "center",
    justifyContent: "center",
  },
  formContainer: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 5,
  },
  required: {
    color: "red",
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: 10,
    padding: Platform.OS === "ios" ? 10 : 0,
    paddingHorizontal: 10,
  },
  input: {
    fontSize: 14,
    fontWeight: "400",
  },
  timeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
    gap: moderateScale(10),
  },
  timeInput: {
    flex: 1,
  },
  timeSelector: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 10,
    padding: 10,
    gap: 10,
  },
  timeText: {
    fontSize: 14,
    fontWeight: "400",
  },
  statusContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: moderateScale(10),
  },
  statusOption: {
    flex: 1,
    padding: 10,
    borderRadius: 10,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  addButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 20,
  },
  addButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
  backToSelectionButton: {
    alignItems: "center",
    marginTop: 10,
    padding: 5,
  },
});

export default AddAppointmentModal;
