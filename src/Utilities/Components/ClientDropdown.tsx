import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import { useTheme } from '../ThemeContext';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import { moderateScale, verticalScale } from '../Styles/responsiveSize';

interface ClientDropdownProps {
  selectedClient: string | null;
  onSelectClient: (clientId: string, clientName: string, phoneNumber: string) => void;
  clients?: any[];
  onAddNewClient?: () => void;
}

const ClientDropdown: React.FC<ClientDropdownProps> = ({
  selectedClient,
  onSelectClient,
  clients,
  onAddNewClient,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { colors, isDarkMode } = useTheme();

  // Find the selected client's name
  const selectedClientName =
    selectedClient && clients
      ? clients.find((client) => client.id === selectedClient)?.name || ""
      : "";

  return (
    <>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
            borderColor: colors.border,
          },
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <Text
          style={[
            styles.dropdownButtonText,
            { color: selectedClient ? colors.text : colors.greyText },
          ]}
        >
          {selectedClientName || "Select client"}
        </Text>
        <CustomIcon Icon={ICONS.ArrowDownIcon} height={12} width={12} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode
                  ? colors.cardBackground
                  : colors.white,
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Client
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={clients || []}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.clientItem,
                    selectedClient === item.id && {
                      backgroundColor: isDarkMode
                        ? colors.bglight
                        : colors.bgsoft,
                    },
                  ]}
                  onPress={() => {
                    onSelectClient(item.id, item.name, item.number || '');
                    setIsModalVisible(false);
                  }}
                >
                  {item.image && (
                    <Image
                      source={{ uri: item.image }}
                      style={styles.clientImage}
                    />
                  )}
                  <View style={styles.clientInfo}>
                    <Text style={[styles.clientName, { color: colors.text }]}>
                      {item.name}
                    </Text>
                    <Text
                      style={[styles.clientNumber, { color: colors.greyText }]}
                    >
                      {item.number || 'No phone number'}
                    </Text>
                  </View>
                  {selectedClient === item.id && (
                    <CustomIcon
                      Icon={ICONS.CheckRightIcon}
                      height={15}
                      width={15}
                    />
                  )}
                </TouchableOpacity>
              )}
              ListFooterComponent={() => (
                <TouchableOpacity
                  style={[
                    styles.addNewButton,
                    {
                      backgroundColor: isDarkMode
                        ? colors.primaryBase
                        : colors.primaryBase,
                    },
                  ]}
                  onPress={() => {
                    setIsModalVisible(false);
                    if (onAddNewClient) {
                      onAddNewClient();
                    }
                  }}
                >
                  <CustomIcon Icon={ICONS.PlusIcon} height={15} width={15} />
                  <Text style={styles.addNewText}>Enter Client Details Manually</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 10,
    padding: 10,
    marginTop: 5,
  },
  dropdownButtonText: {
    fontSize: 14,
    fontWeight: '400',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 10,
    padding: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
  },
  clientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
  },
  clientImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 14,
    fontWeight: '500',
  },
  clientNumber: {
    fontSize: 12,
    marginTop: verticalScale(2),
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  addNewText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default ClientDropdown;
