import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import { useTheme } from '../ThemeContext';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import { moderateScale, verticalScale } from '../Styles/responsiveSize';
import { Service } from '../../Redux/slices/servicesSlice';

interface ServiceDropdownProps {
  selectedService: string | null;
  onSelectService: (
    serviceId: string,
    serviceName: string,
    duration: string
  ) => void;
  services?: Service[];
}

const ServiceDropdown: React.FC<ServiceDropdownProps> = ({
  selectedService,
  onSelectService,
  services,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { colors, isDarkMode } = useTheme();

  // Find the selected service's name
  const selectedServiceName =
    selectedService && services
      ? services.find((service) => service.id === selectedService)
          ?.businessName || ""
      : "";

  return (
    <>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
            borderColor: colors.border,
          },
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <Text
          style={[
            styles.dropdownButtonText,
            { color: selectedService ? colors.text : colors.greyText },
          ]}
        >
          {selectedServiceName || "Select service"}
        </Text>
        <CustomIcon Icon={ICONS.ArrowDownIcon} height={12} width={12} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode
                  ? colors.cardBackground
                  : colors.white,
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Service
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={services || []}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.serviceItem,
                    selectedService === item.id && {
                      backgroundColor: isDarkMode
                        ? colors.bglight
                        : colors.bgsoft,
                    },
                  ]}
                  onPress={() => {
                    onSelectService(item.id, item.businessName, item.duration);
                    setIsModalVisible(false);
                  }}
                >
                  <View style={styles.serviceInfo}>
                    <Text style={[styles.serviceName, { color: colors.text }]}>
                      {item.businessName}
                    </Text>
                    <View style={styles.serviceDetails}>
                      <Text
                        style={[
                          styles.serviceCategory,
                          { color: colors.greyText },
                        ]}
                      >
                        {item.category}
                      </Text>
                      <Text
                        style={[
                          styles.serviceDuration,
                          { color: colors.greyText },
                        ]}
                      >
                        {item.duration}
                      </Text>
                      <Text
                        style={[
                          styles.servicePrice,
                          { color: colors.greyText },
                        ]}
                      >
                        {item.currency} {item.price}
                      </Text>
                    </View>
                  </View>
                  {selectedService === item.id && (
                    <CustomIcon
                      Icon={ICONS.CheckRightIcon}
                      height={15}
                      width={15}
                    />
                  )}
                </TouchableOpacity>
              )}
              ListEmptyComponent={() => (
                <Text style={[styles.emptyText, { color: colors.greyText }]}>
                  No services available
                </Text>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 10,
    padding: 10,
    paddingHorizontal: 15,
  },
  dropdownButtonText: {
    fontSize: 14,
    fontWeight: '400',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    borderRadius: 15,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(15),
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 14,
    fontWeight: '500',
  },
  serviceDetails: {
    flexDirection: 'row',
    marginTop: 5,
    flexWrap: 'wrap',
  },
  serviceCategory: {
    fontSize: 12,
    marginRight: 10,
  },
  serviceDuration: {
    fontSize: 12,
    marginRight: 10,
  },
  servicePrice: {
    fontSize: 12,
  },
  emptyText: {
    textAlign: 'center',
    padding: 20,
    fontSize: 14,
  },
});

export default ServiceDropdown;
