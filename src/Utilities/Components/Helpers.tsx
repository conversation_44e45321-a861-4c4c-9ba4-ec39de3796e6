import {TouchableOpacity} from 'react-native';
import CountryPicker, {
  CountryCode,
  Country,
} from 'react-native-country-picker-modal';

import commonStyles from '../Styles/commonStyles';
import {Colors} from '../Styles/colors';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import styles from './style';

interface PhonePickerProps {
  visible: boolean;
  onSelect: (country: Country) => void;
  onClose?: () => void;
  countryCode?: CountryCode;
  onPress: () => void;
  border?: boolean;
}

export function PhonePicker({
  visible,
  onSelect,
  onClose,
  onPress,
  countryCode = "IN",
  border,
}: PhonePickerProps) {
  return (
    <TouchableOpacity style={styles.picVw} onPress={onPress}>
      <CountryPicker
        key={countryCode} // Ensures re-render when countryCode changes
        visible={visible}
        onSelect={onSelect}
        onClose={onClose}
        theme={{
          ...commonStyles.font14Center,
          onBackgroundTextColor: Colors.maintext,
          backgroundColor: Colors.white,
        }}
        withCallingCode={true}
        withCallingCodeButton
        withFlagButton={false}
        withFilter
        countryCode={countryCode}
        containerButtonStyle={[
          { borderWidth: border == true ? 1 : 0 },
          styles.pickerContainer,
        ]}
      />
      <CustomIcon Icon={ICONS.DropdownIcon} height={6} width={10} />
    </TouchableOpacity>
  );
}
