import React, { createContext, useState, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from './Styles/colors';

// Theme types
export type ThemeType = 'light' | 'dark' | 'system';

// Theme storage key
const THEME_STORAGE_KEY = '@glamup:theme';

// Define theme colors
export const lightTheme = {
  ...Colors,
  background: Colors.bglight,
  text: Colors.black,
  cardBackground: Colors.white,
  border: Colors.bgsoft,
};

export const darkTheme = {
  ...Colors,
  background: '#121212', // Dark background
  text: Colors.white,
  cardBackground: '#1E1E1E', // Slightly lighter than background
  border: '#2C2C2C', // Dark border
  bglight: '#1E1E1E', // Dark background for inputs
  bgsoft: '#2C2C2C', // Dark border
  greyText: '#A0A0A0', // Lighter grey for dark mode
};

// Create the theme context
interface ThemeContextType {
  theme: ThemeType;
  colors: typeof lightTheme;
  setTheme: (theme: ThemeType) => void;
  isDarkMode: boolean;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  colors: lightTheme,
  setTheme: () => {},
  isDarkMode: false,
});

// Create the theme provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [theme, setThemeState] = useState<ThemeType>("light");
  const [isLoading, setIsLoading] = useState(true);

  // Load saved theme from AsyncStorage
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme) {
          setThemeState(savedTheme as ThemeType);
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, []);

  // Save theme to AsyncStorage when it changes
  const setTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  // Determine if dark mode is active
  const isDarkMode = 
    theme === 'dark' || 
    (theme === 'system' && systemColorScheme === 'dark');

  // Get the current theme colors
  const colors = isDarkMode ? darkTheme : lightTheme;

  // Return loading placeholder if still loading
  if (isLoading) {
    return null;
  }

  return (
    <ThemeContext.Provider value={{ theme, colors, setTheme, isDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Create a hook to use the theme context
export const useTheme = () => useContext(ThemeContext);
