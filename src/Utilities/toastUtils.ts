import Toast from 'react-native-toast-message';

/**
 * Utility functions for showing toast notifications
 */

/**
 * Show a success toast notification
 * @param title The main title of the toast
 * @param message The detailed message to display
 * @param duration How long to show the toast (in ms)
 */
export const showSuccessToast = (
  title: string,
  message: string,
  duration: number = 2000
) => {
  Toast.show({
    type: 'success',
    text1: title,
    text2: message,
    visibilityTime: duration,
  });
};

/**
 * Show an error toast notification
 * @param title The main title of the toast
 * @param message The detailed message to display
 * @param duration How long to show the toast (in ms)
 */
export const showErrorToast = (
  title: string,
  message: string,
  duration: number = 2000
) => {
  Toast.show({
    type: 'error',
    text1: title,
    text2: message,
    position: 'bottom',
    visibilityTime: duration,
  });
};

/**
 * Show an info toast notification
 * @param title The main title of the toast
 * @param message The detailed message to display
 * @param duration How long to show the toast (in ms)
 */
export const showInfoToast = (
  title: string,
  message: string,
  duration: number = 2000
) => {
  Toast.show({
    type: 'info',
    text1: title,
    text2: message,
    position: 'bottom',
    visibilityTime: duration,
  });
};

/**
 * Show a warning toast notification
 * @param title The main title of the toast
 * @param message The detailed message to display
 * @param duration How long to show the toast (in ms)
 */
export const showWarningToast = (
  title: string,
  message: string,
  duration: number = 2000
) => {
  Toast.show({
    type: 'warning',
    text1: title,
    text2: message,
    position: 'bottom',
    visibilityTime: duration,
  });
};
