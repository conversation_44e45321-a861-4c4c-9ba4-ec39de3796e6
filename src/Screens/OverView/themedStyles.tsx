import { StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
      padding: 20,
      backgroundColor: colors.background,
    },
    headerRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 20,
    },
    viewModeButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingVertical: 6,
      paddingHorizontal: 12,
      borderRadius: 16,
      gap: 5,
      borderWidth: 1,
      borderColor: colors.border,
    },
    viewModeText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    iconRow: {
      flexDirection: "row",
      gap: 25,
      alignItems: "center",
    },
    dayContainer: {
      alignItems: "center",
      borderRadius: 10,
      paddingHorizontal: 10,
    },
    dayText: {
      fontSize: 12,
      fontWeight: "500",
      color: colors.text,
      marginBottom: 5,
    },
    dateText: {
      fontSize: 16,
      fontWeight: "500",
      color: colors.text,
    },
    selectedDateText: {
      color: colors.white,
    },
    fadedText: {
      color: colors.bgsoft,
    },
    selectedDay: {
      backgroundColor: colors.stateerrorbase,
      padding: 7,
      borderRadius: 25,
    },
    dayWrapper: {
      alignItems: "center",
    },
    dateWrapper: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: "center",
      justifyContent: "center",
      marginTop: 5,
    },
    profileouterview: {
      height: 88,
      width: 177,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderRightWidth: 1,
      borderColor: colors.border,
    },
    datesidebar: {
      height: 88,
      width: 48,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderRightWidth: 1,
      borderColor: colors.border,
    },
    timeSlot: {
      paddingHorizontal: 5,
      paddingVertical: 50,
      alignItems: "center",
    },
    datesidebar2: {
      height: 88,
      borderBottomWidth: 1,
      borderRightWidth: 1,
      borderColor: colors.border,
    },
    calendarbtn: {
      backgroundColor: colors.greyText,
      padding: 10,
      borderRadius: 20,
      flexDirection: "row",
      alignItems: "center",
      alignSelf: "flex-end",
      gap: moderateScale(5),
      position: "absolute",
      bottom: 120,
      right: 20,
      zIndex: 1,
    },
    appointmentItem: {
      flexDirection: "row",
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(15),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      borderRadius: 8,
      marginHorizontal: moderateScale(10),
      marginVertical: verticalScale(5),
      position: "relative",
      overflow: "hidden",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    accentLine: {
      width: 3,
      position: "absolute",
      left: 0,
      top: 0,
      bottom: 0,
    },
    timeContainer: {
      width: moderateScale(60),
      justifyContent: "center",
    },
    timeText: {
      fontSize: 16,
      fontWeight: "bold",
      color: colors.text,
    },
    detailsContainer: {
      flex: 1,
      justifyContent: "center",
    },
    clientName: {
      fontSize: 16,
      fontWeight: "bold",
      color: colors.text,
    },
    serviceText: {
      fontSize: 14,
      color: colors.greyText,
      marginTop: verticalScale(2),
    },
    durationText: {
      fontSize: 12,
      color: colors.greyText,
      marginTop: verticalScale(2),
    },
    teamMemberText: {
      fontSize: 12,
      color: colors.greyText,
      marginTop: verticalScale(2),
    },
    cancelledText: {
      color: "#FF0000", // Red color for cancelled appointments
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: verticalScale(20),
    },
    emptyText: {
      fontSize: 16,
      color: colors.greyText,
    },
    listContent: {
      paddingBottom: verticalScale(20),
    },
    loadingContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    noInternetContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: moderateScale(50),
      gap: verticalScale(10),
    },
    tryAgainButton: {
      flexDirection: "row",
      alignItems: "center",
      gap: verticalScale(5),
      paddingVertical: verticalScale(8),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingHorizontal: moderateScale(10),
      borderRadius: 5,
      elevation: 2,
    },
    tryAgainText: {
      color: colors.text,
    },
    weekNavigation: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 20,
      gap: 10,
    },
    monthDropdownButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 16,
      gap: 5,
      borderWidth: 1,
      borderColor: colors.border,
      minWidth: 100,
    },
    monthDropdownText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    safeAreaContainer: {
      flex: 1,
      gap: verticalScale(10),
      backgroundColor: colors.background,
    },
  });
};
