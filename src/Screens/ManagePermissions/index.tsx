import AsyncStorage from "@react-native-async-storage/async-storage"; // Import AsyncStorage
import React, { useEffect, useState } from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ManagePermissionProps } from "../../Navigation/Typings";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { showSuccessToast } from "../../Utilities/toastUtils";
import { useThemedStyles } from "./themedStyles";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";

const initialRolesData = [
  {
    id: "1",
    title: "General user",
    permissions: [
      {
        id: "1",
        title: "Can access own calendar",
        description: "Allows the user to view their own calendar.",
        enabled: false,
      },
      {
        id: "2",
        title: "Can access other team members calendar",
        description:
          "Allows the user to view the calendars of other team members.",
        enabled: true,
      },
      {
        id: "3",
        title: "Can book appointments",
        description: "Permits the user to schedule appointments.",
        enabled: true,
      },
      {
        id: "4",
        title: "Can set timeoff",
        description: "Enables the user to mark time off periods.",
        enabled: true,
      },
      {
        id: "5",
        title: "Can see client info",
        description: "Allows the user to view client information.",
        enabled: false,
      },
    ],
  },
  {
    id: "2",
    title: "Test role",
    permissions: [
      {
        id: "1",
        title: "Can access own calendar",
        description: "Allows the user to view their own calendar.",
        enabled: false,
      },
      {
        id: "2",
        title: "Can access other team members calendar",
        description:
          "Allows the user to view the calendars of other team members.",
        enabled: false,
      },
      {
        id: "3",
        title: "Can book appointments",
        description: "Permits the user to schedule appointments.",
        enabled: false,
      },
      {
        id: "4",
        title: "Can set timeoff",
        description: "Enables the user to mark time off periods.",
        enabled: false,
      },
    ],
  },
];

const ManagePermissions = ({ navigation }: ManagePermissionProps) => {
  const [dropdownState, setDropdownState] = useState<{
    [key: string]: boolean;
  }>({
    "1": true,
    "2": true,
  });

  const [rolesData, setRolesData] = useState(initialRolesData);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Key for AsyncStorage
  const STORAGE_KEY = "@roles_data";

  // Load roles data from AsyncStorage when component mounts
  useEffect(() => {
    const loadRolesData = async () => {
      try {
        const storedData = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedData) {
          setRolesData(JSON.parse(storedData));
        }
      } catch (error) {
        console.error("Error loading roles data from AsyncStorage:", error);
      }
    };
    loadRolesData();
  }, []);

  // Save roles data to AsyncStorage whenever it changes
  useEffect(() => {
    const saveRolesData = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(rolesData));
      } catch (error) {
        console.error("Error saving roles data to AsyncStorage:", error);
      }
    };
    saveRolesData();
  }, [rolesData]);

  const toggleDropdown = (id: string) => {
    setDropdownState((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  const togglePermission = (roleId: string, permissionId: string) => {
    // Find the current role and permission to get their names for the toast
    const currentRole = rolesData.find((role) => role.id === roleId);
    const currentPermission = currentRole?.permissions.find(
      (perm) => perm.id === permissionId
    );
    const isCurrentlyEnabled = currentPermission?.enabled;

    setRolesData((prevRoles) =>
      prevRoles.map((role) =>
        role.id === roleId
          ? {
              ...role,
              permissions: role.permissions.map((permission) =>
                permission.id === permissionId
                  ? { ...permission, enabled: !permission.enabled }
                  : permission
              ),
            }
          : role
      )
    );

    // Show toast notification
    if (currentPermission) {
      const newState = !isCurrentlyEnabled;
      const roleName = currentRole?.title || "Role";
      const permissionName = currentPermission.title;

      showSuccessToast(
        `Permission ${newState ? "Enabled" : "Disabled"}`,
        `${permissionName} is now ${
          newState ? "enabled" : "disabled"
        } for ${roleName}`
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Unified Header */}
      <UnifiedHeader
        title="Permissions"
        onBackPress={() => navigation.goBack()}
        rightButtonText="Add"
        onRightButtonPress={() => navigation.navigate("addPermissionRole")}
      />

      <View style={{ paddingHorizontal: verticalScale(10) }}>
        {/* Roles List */}
        <FlatList
          data={rolesData}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => (
            <View style={styles.permissionContainer}>
              <TouchableOpacity
                onPress={() => toggleDropdown(item.id)}
                style={[
                  styles.dropdownHeader,
                  !dropdownState[item.id] && { borderBottomWidth: 0 },
                ]}
              >
                <Text style={styles.roleTitle}>{item.title}</Text>
                <CustomIcon
                  Icon={
                    dropdownState[item.id]
                      ? ICONS.ArrowUpIcon
                      : ICONS.ArrowDownIcon
                  }
                  height={20}
                  width={20}
                />
              </TouchableOpacity>

              {dropdownState[item.id] && (
                <FlatList
                  data={item.permissions}
                  keyExtractor={(perm) => perm.id}
                  renderItem={({ item: perm }) => (
                    <View style={styles.permissionItem}>
                      <View
                        style={{
                          flex: 1,
                        }}
                      >
                        <Text style={styles.permissionText}>{perm.title}</Text>
                        <Text style={styles.permissionDesc}>
                          {perm.description}
                        </Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => togglePermission(item.id, perm.id)}
                        activeOpacity={0.8}
                        style={[
                          styles.switchContainer,
                          {
                            backgroundColor: perm.enabled
                              ? colors.primaryBase
                              : colors.bgsoft,
                          },
                        ]}
                      >
                        <View
                          style={[
                            styles.circle,
                            {
                              transform: [
                                { translateX: perm.enabled ? 22 : 2 },
                              ],
                            },
                          ]}
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                />
              )}
            </View>
          )}
        />
      </View>
    </SafeAreaView>
  );
};

export default ManagePermissions;
