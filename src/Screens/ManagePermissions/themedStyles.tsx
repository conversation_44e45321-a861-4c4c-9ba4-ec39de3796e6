import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    permissionContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: verticalScale(10),
      borderRadius: 16,
      borderBottomColor: colors.border,
      marginTop: verticalScale(20),
    },
    dropdownHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingVertical: verticalScale(10),
    },
    permissionItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: verticalScale(10),
    },
    permissionText: {
      fontSize: 14,
      color: colors.text,
      width: "85%",
    },
    permissionDesc: {
      fontSize: 12,
      color: colors.greyText,
      width: "85%",
    },
    toggleWrapper: {
      alignItems: "center",
      justifyContent: "center",
    },
    switchContainer: {
      width: 45,
      height: 25,
      borderRadius: 15,
      justifyContent: "center",
      padding: 2,
    },
    circle: {
      width: 15,
      height: 15,
      borderRadius: 13,
      borderWidth: 5,
      borderColor: colors.white,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
    },
    roleTitle: {
      fontSize: 18,
      fontWeight: "500",
      color: colors.text,
    },
  });
};
