import React, { useEffect, useState } from "react";
import {
  FlatList,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { updateLocationInfo } from "../../../Redux/slices/businessSlice";
import { RootState } from "../../../Redux/store";
import {
  citiesByCountry,
  countries,
  regionsByCity,
} from "../../../Seeds/CountriesData";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedLocationStyles } from "./themedLocationStyles";

// Define types for Dropdown props
interface DropdownProps {
  visible: boolean;
  options: string[];
  onSelect: (value: string) => void;
  onClose: () => void;
}

const Dropdown = ({ visible, options, onSelect, onClose }: DropdownProps) => {
  const styles = useThemedLocationStyles();

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.dropdownModal}>
          <FlatList
            data={options}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => onSelect(item)}
              >
                <Text style={styles.dropdownItemText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

interface LocationProps {
  onValidationChange?: (isValid: boolean) => void;
}

const Location: React.FC<LocationProps> = ({ onValidationChange }) => {
  const dispatch = useDispatch();
  const locationData = useSelector(
    (state: RootState) => state.business.location
  );

  const { colors } = useTheme();
  const styles = useThemedLocationStyles();

  const [selectedCountry, setSelectedCountry] = useState(
    locationData.country || "India"
  );
  const [selectedCity, setSelectedCity] = useState(locationData.city || "");
  const [selectedRegion, setSelectedRegion] = useState(
    locationData.region || ""
  );
  const [streetAddress, setStreetAddress] = useState(
    locationData.streetAddress || ""
  );
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<string[]>([]);
  const [dropdownSetter, setDropdownSetter] = useState<
    ((value: string) => void) | null
  >(null);
  const [isValid, setIsValid] = useState(false);

  // Validate location data and save to Redux
  useEffect(() => {
    const valid =
      selectedCountry.trim() !== "" &&
      selectedCity.trim() !== "" &&
      selectedRegion.trim() !== "" &&
      streetAddress.trim() !== "";

    setIsValid(valid);

    // Save to Redux
    dispatch(
      updateLocationInfo({
        country: selectedCountry,
        city: selectedCity,
        region: selectedRegion,
        streetAddress: streetAddress,
      })
    );

    if (onValidationChange) {
      onValidationChange(valid);
    }
  }, [selectedCountry, selectedCity, selectedRegion, streetAddress, dispatch]);

  const openDropdown = (options: string[], setter: (value: string) => void) => {
    setDropdownOptions(options);
    setDropdownSetter(() => setter);
    setDropdownVisible(true);
  };

  const handleSelect = (value: string) => {
    if (dropdownSetter) {
      // If changing country, reset city and region
      if (dropdownSetter === setSelectedCountry) {
        setSelectedCity("");
        setSelectedRegion("");
      }
      // If changing city, reset region
      else if (dropdownSetter === setSelectedCity) {
        setSelectedRegion("");
      }

      dropdownSetter(value);
    }
    setDropdownVisible(false);
  };

  // Find the country code for the selected country
  const selectedCountryObj = countries.find((c) => c.name === selectedCountry);
  const countryCode = selectedCountryObj ? selectedCountryObj.code : "";

  // Get available cities based on selected country code
  const availableCities = countryCode ? citiesByCountry[countryCode] || [] : [];

  // Get available regions based on selected city
  const availableRegions = selectedCity
    ? regionsByCity[selectedCity] || []
    : [];

  return (
    <View style={{ flex: 1 }}>
      <Dropdown
        visible={dropdownVisible}
        options={dropdownOptions}
        onSelect={handleSelect}
        onClose={() => setDropdownVisible(false)}
      />

      {/* Country */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Country <Text style={styles.required}>*</Text>
        </Text>
        <TouchableOpacity
          style={styles.dropdownContainer}
          onPress={() =>
            openDropdown(
              countries.map((c) => c.name),
              setSelectedCountry
            )
          }
        >
          <Text
            style={[
              styles.textInput,
              selectedCountry ? styles.selectedText : styles.placeholderText,
            ]}
          >
            {selectedCountry || "Select a country"}
          </Text>
          <CustomIcon Icon={ICONS.DropdownIcon} height={12} width={12} />
        </TouchableOpacity>
      </View>

      {/* City */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          City <Text style={styles.required}>*</Text>
        </Text>
        <TouchableOpacity
          style={styles.dropdownContainer}
          onPress={() => openDropdown(availableCities, setSelectedCity)}
          disabled={!selectedCountry}
        >
          <Text
            style={[
              styles.textInput,
              selectedCity ? styles.selectedText : styles.placeholderText,
            ]}
          >
            {selectedCity || "Select a city"}
          </Text>
          <CustomIcon Icon={ICONS.DropdownIcon} height={12} width={12} />
        </TouchableOpacity>
      </View>

      {/* Region */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Region <Text style={styles.required}>*</Text>
        </Text>
        <TouchableOpacity
          style={styles.dropdownContainer}
          onPress={() => openDropdown(availableRegions, setSelectedRegion)}
          disabled={!selectedCity}
        >
          <Text
            style={[
              styles.textInput,
              selectedRegion ? styles.selectedText : styles.placeholderText,
            ]}
          >
            {selectedRegion || "Select a region"}
          </Text>
          <CustomIcon Icon={ICONS.DropdownIcon} height={12} width={12} />
        </TouchableOpacity>
      </View>

      {/* Street Address */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Street Address <Text style={styles.required}>*</Text>
        </Text>
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.textInput}
            placeholder="123 Main Street"
            placeholderTextColor={colors.greyText}
            value={streetAddress}
            onChangeText={setStreetAddress}
          />
        </View>
      </View>
    </View>
  );
};

export default Location;
