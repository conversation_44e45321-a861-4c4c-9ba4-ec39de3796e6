import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale, height } from '../../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../../Utilities/ThemeContext';

export const useThemedLocationStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    inputContainer: {
      borderRadius: 12,
      padding: verticalScale(10),
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 12,
      color: colors.text,
    },
    required: {
      color: colors.stateerrorbase,
    },
    textInputContainer: {
      paddingHorizontal: moderateScale(12),
      paddingVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(5),
    },
    textInput: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
    },
    dropdownContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: moderateScale(12),
      paddingVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(5),
    },
    selectedText: {
      color: colors.text,
    },
    placeholderText: {
      color: colors.greyText,
    },
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    dropdownModal: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      width: "80%",
      borderRadius: 10,
      padding: 10,
      maxHeight: height / 2,
    },
    dropdownItem: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderBottomColor: colors.bgsoft,
    },
    dropdownItemText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
    },
  });
};
