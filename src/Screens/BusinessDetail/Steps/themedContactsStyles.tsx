import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale } from '../../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../../Utilities/ThemeContext';

export const useThemedContactsStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    inputContainer: {
      borderRadius: 12,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(5),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      marginBottom: verticalScale(15),
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
    },
    input: {
      fontSize: 14,
      fontWeight: '400',
      marginHorizontal: moderateScale(5),
      marginVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      flex: 1,
      color: colors.text,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      color: colors.text,
    },
  });
};
