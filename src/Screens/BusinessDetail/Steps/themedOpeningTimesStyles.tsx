import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale } from '../../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../../Utilities/ThemeContext';

export const useThemedOpeningTimesStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    inputContainer: {
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: colors.stateerrorbase,
    },
    itemContainer: {
      padding: 10,
    },
    checkboxContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: 2,
      borderColor: colors.bgsoft,
      backgroundColor: isDarkMode ? 'transparent' : colors.white,
      borderRadius: 4,
      marginRight: 8,
      justifyContent: "center",
      alignItems: "center",
    },
    checkedBox: {
      backgroundColor: colors.primaryBase,
      borderColor: colors.primaryBase,
    },
    timeContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 15,
      justifyContent: "space-between",
      gap: moderateScale(20),
    },
    timeBox: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      borderWidth: 1,
      borderColor: colors.bgsoft,
      padding: 10,
      borderRadius: 8,
      gap: moderateScale(20),
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    timeText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
    },
    addButton: {},
    toggleClosedButton: {
      padding: moderateScale(10),
    },
    actionButtons: {
      flexDirection: "row",
      alignItems: "center",
    },
    copyPasteButton: {
      paddingHorizontal: moderateScale(10),
    },
    copyPasteText: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.primaryBase,
    },
    disabledButton: {
      opacity: 0.5,
    },
    disabledText: {
      color: colors.greyText,
    },
    dayText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
    },
    dayTextChecked: {
      color: colors.primaryBase,
    },
    dayTextClosed: {
      color: colors.greyText,
    },
  });
};
