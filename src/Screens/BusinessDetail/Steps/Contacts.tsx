import { Text, TextInput, View } from 'react-native';
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { updateContactInfo } from "../../../Redux/slices/businessSlice";
import { RootState } from "../../../Redux/store";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedContactsStyles } from "./themedContactsStyles";

const socialLinks = [
  {
    label: "Email",
    placeholder: "<EMAIL>",
    icon: ICONS.EmailIcon,
  },
  {
    label: "Facebook",
    placeholder: "https://facebook.com/example",
    icon: ICONS.Facebook,
  },
  {
    label: "Instagram",
    placeholder: "https://instagram.com/example",
    icon: ICONS.Instagram,
  },
];

interface ContactsProps {
  onValidationChange?: (isValid: boolean) => void;
}

type SocialValues = {
  Email: string;
  Facebook: string;
  Instagram: string;
  [key: string]: string;
};

const Contacts: React.FC<ContactsProps> = ({ onValidationChange }) => {
  const dispatch = useDispatch();
  const contactsData = useSelector(
    (state: RootState) => state.business.contacts
  );

  const { colors } = useTheme();
  const styles = useThemedContactsStyles();

  const [values, setValues] = useState<SocialValues>({
    Email: contactsData.email || "",
    Facebook: contactsData.facebook || "",
    Instagram: contactsData.instagram || "",
  });
  const [isValid, setIsValid] = useState(false);

  // Validate email format and save to Redux
  useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isEmailValid =
      values.Email.trim() === "" || emailRegex.test(values.Email);

    setIsValid(isEmailValid);

    // Save to Redux
    dispatch(
      updateContactInfo({
        email: values.Email,
        facebook: values.Facebook,
        instagram: values.Instagram,
      })
    );

    if (onValidationChange) {
      onValidationChange(isEmailValid);
    }
  }, [values, dispatch]);

  const handleChange = (key: string, value: string) =>
    setValues((prev) => ({ ...prev, [key]: value }));

  return (
    <View style={{ flex: 1 }}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        {socialLinks.map(({ label, placeholder, icon }) => (
          <View key={label} style={styles.inputContainer}>
            <Text style={styles.label}>{label}</Text>
            <View style={styles.row}>
              <CustomIcon Icon={icon} width={15} height={14.17} />
              <TextInput
                style={styles.input}
                numberOfLines={1}
                placeholder={placeholder}
                placeholderTextColor={colors.greyText}
                value={values[label]}
                onChangeText={(text) => handleChange(label, text)}
                keyboardType="email-address"
              />
            </View>
          </View>
        ))}
      </KeyboardAwareScrollView>
    </View>
  );
};

export default Contacts;
