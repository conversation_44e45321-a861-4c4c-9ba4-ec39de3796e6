import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale } from '../../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../../Utilities/ThemeContext';

export const useThemedDetailStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    inputContainer: {
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 5,
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: "700",
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: colors.stateerrorbase,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      marginHorizontal: moderateScale(10),
      marginVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      color: colors.text,
    },
    textArea: {
      height: 98,
      textAlignVertical: "top",
    },
    resizetext: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      paddingVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      marginRight: moderateScale(10),
      gap: moderateScale(3),
    },
    charCount: {
      fontSize: 12,
      fontWeight: "400",
      textAlign: "right",
      color: colors.text,
    },
    phoneRow: {
      flexDirection: "row",
      alignItems: "center",
    },
    phoneInputContainer: {
      width: "70%",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    websiteRow: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      paddingHorizontal: moderateScale(10),
    },
    flexInput: {
      fontSize: 14,
      fontWeight: "400",
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    nextButton: {
      marginTop: 20,
      marginHorizontal: 16,
      paddingVertical: 14,
      borderRadius: 12,
      alignItems: "center",
      backgroundColor: colors.maintext,
    },
    nextButtonText: {
      fontSize: 16,
      fontWeight: "500",
      color: colors.white,
    },
    phoneContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
  });
};
