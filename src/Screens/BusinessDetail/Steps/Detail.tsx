import React from 'react';
import { Text, TextInput, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { ThemedPhonePicker } from "../../../Utilities/Components/ThemedHelpers";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedDetailStyles } from "./themedDetailStyles";

const Detail = ({
  description,
  setDescription,
  businessName,
  setBusinessName,
  phoneNumber,
  setPhoneNumber,
  countryVisible,
  setCountryVisible,
  countryCode,
  setCountryCode,
  callingCode,
  setCallingCode,
  websiteUrl,
  setWebsiteUrl,
}: any) => {
  const { colors } = useTheme();
  const styles = useThemedDetailStyles();

  const handleCountrySelect = (country: any) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode ? country.callingCode[0] : "1");
  };

  const toggleCountryPicker = () => {
    setCountryVisible((prev: any) => !prev);
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Business Name <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="The King's Cut"
            placeholderTextColor={colors.greyText}
            value={businessName}
            onChangeText={setBusinessName}
            keyboardType="default"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Give a brief description about your business and the services you provide..."
            placeholderTextColor={colors.greyText}
            multiline
            value={description}
            onChangeText={setDescription}
          />
          <View style={styles.resizetext}>
            <Text style={styles.charCount}>{description.length}/200</Text>
            <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Phone Number <Text style={styles.required}>*</Text>
          </Text>
          <View style={styles.phoneRow}>
            <View style={styles.phoneContainer}>
              <ThemedPhonePicker
                visible={countryVisible}
                countryCode={countryCode}
                onSelect={handleCountrySelect}
                onPress={toggleCountryPicker}
              />
            </View>
            <View style={styles.phoneInputContainer}>
              <TextInput
                style={styles.input}
                placeholder="00 00 0000"
                placeholderTextColor={colors.greyText}
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="number-pad"
              />
            </View>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Website</Text>
          <View style={styles.websiteRow}>
            <CustomIcon Icon={ICONS.WebsiteUrlIcon} width={16} height={16} />
            <TextInput
              style={styles.flexInput}
              placeholder="https://example.com"
              placeholderTextColor={colors.greyText}
              value={websiteUrl}
              onChangeText={setWebsiteUrl}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default Detail;
