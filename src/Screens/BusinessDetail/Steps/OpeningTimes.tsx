import React, { useEffect, useState } from "react";
import {
  ScrollView,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import DatePicker from "react-native-date-picker";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { updateOpeningTimes } from "../../../Redux/slices/businessSlice";
import { RootState } from "../../../Redux/store";
import { moderateScale } from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedOpeningTimesStyles } from "./themedOpeningTimesStyles";

// Initial days data
const days = [
  {
    day: "Monday",
    isChecked: true,
    isClosed: false,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Tuesday",
    isChecked: true,
    isClosed: false,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Wednesday",
    isChecked: true,
    isClosed: false,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Thursday",
    isChecked: true,
    isClosed: false,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Friday",
    isChecked: true,
    isClosed: false,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Saturday",
    isChecked: false,
    isClosed: true,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
  {
    day: "Sunday",
    isChecked: false,
    isClosed: true,
    timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
  },
];

// Helper function to format time
const formatDateToTime = (date: Date): string => {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
};

// Helper function to parse time string to Date
const parseTimeToDate = (timeString: string): Date => {
  const [hours, minutes] = timeString.split(":").map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  return date;
};

interface TimePickerProps {
  time: string;
  onTimeChange: (time: string) => void;
}

interface OpeningTimesProps {
  onValidationChange?: (isValid: boolean) => void;
}

const TimeSelector: React.FC<TimePickerProps> = ({ time, onTimeChange }) => {
  const [showPicker, setShowPicker] = useState(false);
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedOpeningTimesStyles();

  return (
    <View style={{ flex: 1 }}>
      <TouchableOpacity
        style={styles.timeBox}
        onPress={() => setShowPicker(true)}
      >
        <Text style={styles.timeText}>{time}</Text>
        <CustomIcon Icon={ICONS.DropdownIcon} width={8} height={8} />
      </TouchableOpacity>

      <DatePicker
        modal
        open={showPicker}
        date={parseTimeToDate(time)}
        mode="time"
        theme={isDarkMode ? "dark" : "light"}
        onConfirm={(selectedTime) => {
          setShowPicker(false);
          const formattedTime = formatDateToTime(selectedTime);
          onTimeChange(formattedTime);
        }}
        onCancel={() => setShowPicker(false)}
      />
    </View>
  );
};

const OpeningTimes: React.FC<OpeningTimesProps> = ({ onValidationChange }) => {
  const dispatch = useDispatch();
  const savedOpeningTimes = useSelector(
    (state: RootState) => state.business.openingTimes
  );

  const { colors } = useTheme();
  const styles = useThemedOpeningTimesStyles();

  const [checkedDays, setCheckedDays] = useState(
    savedOpeningTimes && savedOpeningTimes.length > 0 ? savedOpeningTimes : days
  );
  const [copiedDay, setCopiedDay] = useState<{
    isChecked: boolean;
    isClosed: boolean;
    timeSlots: { startTime: string; endTime: string }[];
  } | null>(null);
  const [isValid, setIsValid] = useState(false);

  // Validate that at least one day is checked or marked as closed
  // and save to Redux
  useEffect(() => {
    const valid = checkedDays.some((day) => day.isChecked || day.isClosed);
    setIsValid(valid);

    // Save to Redux
    dispatch(updateOpeningTimes(checkedDays));

    if (onValidationChange) {
      onValidationChange(valid);
    }
  }, [checkedDays, onValidationChange, dispatch]);

  // Toggle day checked state
  const toggleCheck = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = checkedDays.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, toggle the isChecked property
      return {
        ...day,
        isChecked: !day.isChecked,
        timeSlots: [...day.timeSlots],
      };
    });

    setCheckedDays(newDays);
  };

  // Toggle day closed state
  const toggleClosed = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = checkedDays.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, toggle the isClosed property
      const isClosed = !day.isClosed;
      return {
        ...day,
        isClosed,
        // If closing the day, uncheck it
        isChecked: isClosed ? false : day.isChecked,
        timeSlots: [...day.timeSlots],
      };
    });

    setCheckedDays(newDays);
  };

  // Add a new time slot to a day
  const addTimeSlot = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = checkedDays.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots and add a new one
      return {
        ...day,
        timeSlots: [...day.timeSlots, { startTime: "09:00", endTime: "17:00" }],
      };
    });

    setCheckedDays(newDays);
  };

  // Remove a time slot from a day
  const removeTimeSlot = (dayIndex: number, slotIndex: number) => {
    if (checkedDays[dayIndex].timeSlots.length <= 1) {
      return; // Don't remove if it's the only time slot
    }

    // Create a deep copy of the entire state
    const newDays = checkedDays.map((day, i) => {
      if (i !== dayIndex) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots without the one to remove
      return {
        ...day,
        timeSlots: day.timeSlots.filter((_, j) => j !== slotIndex),
      };
    });

    setCheckedDays(newDays);
  };

  // Update a time slot
  const updateTimeSlot = (
    dayIndex: number,
    slotIndex: number,
    field: "startTime" | "endTime",
    value: string
  ) => {
    console.log(
      `Updating time slot: day=${dayIndex}, slot=${slotIndex}, field=${field}, value=${value}`
    );

    // Create a deep copy of the entire state to avoid modifying read-only properties
    const newDays = checkedDays.map((day, i) => {
      if (i !== dayIndex) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots
      const newTimeSlots = day.timeSlots.map((slot, j) => {
        if (j !== slotIndex) return { ...slot };
        // For the slot we're updating, create a new object with the updated field
        return { ...slot, [field]: value };
      });

      return { ...day, timeSlots: newTimeSlots };
    });

    setCheckedDays(newDays);
  };

  // Copy day settings
  const copyDaySettings = (index: number) => {
    const { isChecked, isClosed, timeSlots } = checkedDays[index];
    setCopiedDay({
      isChecked,
      isClosed,
      timeSlots: JSON.parse(JSON.stringify(timeSlots)), // Deep copy
    });
  };

  // Paste day settings
  const pasteDaySettings = (index: number) => {
    if (!copiedDay) return;

    // Create a deep copy of the entire state
    const newDays = checkedDays.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, apply the copied settings
      return {
        ...day,
        isChecked: copiedDay.isChecked,
        isClosed: copiedDay.isClosed,
        // Create a deep copy of the copied time slots
        timeSlots: JSON.parse(JSON.stringify(copiedDay.timeSlots)),
      };
    });

    setCheckedDays(newDays);
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.inputContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ marginVertical: moderateScale(10) }}
        >
          {checkedDays?.map((item, index) => (
            <View key={item.day} style={styles.itemContainer}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  {!item.isClosed && (
                    <TouchableOpacity
                      style={styles.checkboxContainer}
                      onPress={() => toggleCheck(index)}
                      activeOpacity={0.8}
                    >
                      {item.isChecked ? (
                        <View style={[styles.checkbox, styles.checkedBox]}>
                          <CustomIcon
                            Icon={ICONS.CheckRightIcon}
                            height={12}
                            width={12}
                          />
                        </View>
                      ) : (
                        <View style={styles.checkbox} />
                      )}
                    </TouchableOpacity>
                  )}
                  <Text
                    style={[
                      styles.dayText,
                      item.isClosed && styles.dayTextClosed,
                      item.isChecked && styles.dayTextChecked,
                    ]}
                  >
                    {item.day}
                  </Text>
                </View>
                <View style={styles.actionButtons}>
                  {!item.isClosed && (
                    <>
                      <TouchableOpacity
                        onPress={() => copyDaySettings(index)}
                        style={styles.copyPasteButton}
                        activeOpacity={0.8}
                      >
                        <Text style={styles.copyPasteText}>Copy</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => pasteDaySettings(index)}
                        style={[
                          styles.copyPasteButton,
                          !copiedDay && styles.disabledButton,
                        ]}
                        activeOpacity={0.8}
                        disabled={!copiedDay}
                      >
                        <Text
                          style={[
                            styles.copyPasteText,
                            !copiedDay && styles.disabledText,
                          ]}
                        >
                          Paste
                        </Text>
                      </TouchableOpacity>
                    </>
                  )}
                  <TouchableOpacity
                    style={styles.toggleClosedButton}
                    onPress={() => toggleClosed(index)}
                    activeOpacity={0.8}
                  >
                    <Text
                      style={{
                        ...styles.copyPasteText,
                        color: item.isClosed
                          ? colors.primaryBase
                          : colors.stateerrorbase,
                      }}
                    >
                      {item.isClosed ? "Open" : "Closed"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Time slots */}
              {!item.isClosed &&
                item.isChecked &&
                item.timeSlots.map((slot, slotIndex) => (
                  <View key={`slot-${slotIndex}`} style={styles.timeContainer}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        flex: 1,
                        gap: moderateScale(5),
                      }}
                    >
                      <TimeSelector
                        time={slot.startTime}
                        onTimeChange={(newTime: string) =>
                          updateTimeSlot(index, slotIndex, "startTime", newTime)
                        }
                      />
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "400",
                          textAlign: "center",
                          color: colors.text,
                        }}
                      >
                        {" "}
                        -{" "}
                      </Text>
                      <TimeSelector
                        time={slot.endTime}
                        onTimeChange={(newTime: string) =>
                          updateTimeSlot(index, slotIndex, "endTime", newTime)
                        }
                      />
                    </View>
                    {item.timeSlots.length > 1 && (
                      <TouchableOpacity
                        onPress={() => removeTimeSlot(index, slotIndex)}
                        disabled={item.timeSlots.length === 1}
                      >
                        <CustomIcon
                          Icon={ICONS.RedCrossIcon}
                          width={12}
                          height={12}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}

              {/* Add time slot button */}
              {!item.isClosed && item.isChecked && (
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => addTimeSlot(index)}
                  activeOpacity={0.8}
                >
                  <Text
                    style={{
                      ...styles.copyPasteText,
                      textAlign: "center",
                      marginTop: 10,
                    }}
                  >
                    + Add time slot
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

export default OpeningTimes;
