import { useFocusEffect } from "@react-navigation/native";
import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import {
  updateBusinessDetails,
  updateContactInfo,
  updateLocationInfo,
  updateOpeningTimes,
  setSetupComplete,
} from "../../Redux/slices/businessSlice";
import { setIsbusineesSetup } from "../../Redux/slices/initialSlice";
import { RootState } from "../../Redux/store";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import Contacts from "./Steps/Contacts";
import Detail from "./Steps/Detail";
import Location from "./Steps/Location";
import OpeningTimes from "./Steps/OpeningTimes";
import { useThemedStyles } from "./themedStyles";

const tabs = [
  {
    id: "details",
    label: "Details",
    selectedIcon: ICONS.DetailsIcon,
    unselectedIcon: ICONS.ProfileTabIcon,
  },
  {
    id: "location",
    label: "Location",
    selectedIcon: ICONS.LocationSelected,
    unselectedIcon: ICONS.LocationUnselected,
  },
  {
    id: "opening",
    label: "Opening times",
    selectedIcon: ICONS.SelectedOpeningTime,
    unselectedIcon: ICONS.OpeningTimeIcon,
  },
  {
    id: "contacts",
    label: "Contacts",
    selectedIcon: ICONS.SelectedContacts,
    unselectedIcon: ICONS.EmailIcon,
  },
];

const BusinessDetail = ({ navigation }: any) => {
  const dispatch = useDispatch();
  const businessData = useSelector((state: RootState) => state.business);

  const [activeIndex, setActiveIndex] = useState(0);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const handleGoBack = () => {
    navigation.goBack();
  };

  // Initialize state from Redux
  const [description, setDescription] = useState(
    businessData.details.description || ""
  );
  const [businessName, setBusinessName] = useState(
    businessData.details.businessName || ""
  );
  const [phoneNumber, setPhoneNumber] = useState(
    businessData.details.phoneNumber || ""
  );
  const [countryVisible, setCountryVisible] = useState(false);
  const [countryCode, setCountryCode] = useState(
    businessData.details.countryCode || "IN"
  );
  const [callingCode, setCallingCode] = useState(
    businessData.details.callingCode || ""
  );
  const [websiteUrl, setWebsiteUrl] = useState(
    businessData.details.websiteUrl || ""
  );

  const [stepValidation, setStepValidation] = useState({
    details: false,
    location: false,
    opening: false,
    contacts: true, // Contacts are optional, so default to true
  });

  useEffect(() => {
    const isBusinessNameValid = businessName.trim().length > 0;
    const isPhoneValid = /^\d+$/.test(phoneNumber);
    setStepValidation((prev) => ({
      ...prev,
      details: isBusinessNameValid && isPhoneValid,
    }));
  }, [businessName, phoneNumber]);

  const isCurrentStepValid = () => {
    const currentTabId = tabs[activeIndex]?.id;
    return stepValidation[currentTabId as keyof typeof stepValidation] || false;
  };

  useFocusEffect(
    useCallback(() => {
      setActiveIndex(0);
    }, [])
  );

  const renderTabContent = () => {
    switch (tabs[activeIndex]?.id) {
      case "details":
        return (
          <Detail
            key="details"
            description={description}
            setDescription={setDescription}
            businessName={businessName}
            setBusinessName={setBusinessName}
            phoneNumber={phoneNumber}
            setPhoneNumber={setPhoneNumber}
            countryVisible={countryVisible}
            setCountryVisible={setCountryVisible}
            countryCode={countryCode}
            setCountryCode={setCountryCode}
            callingCode={callingCode}
            setCallingCode={setCallingCode}
            websiteUrl={websiteUrl}
            setWebsiteUrl={setWebsiteUrl}
          />
        );
      case "location":
        return (
          <Location
            key="location"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, location: isValid }))
            }
          />
        );
      case "opening":
        return (
          <OpeningTimes
            key="opening"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, opening: isValid }))
            }
          />
        );
      case "contacts":
        return (
          <Contacts
            key="contact"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, contacts: isValid }))
            }
          />
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <UnifiedHeader title="Setup your business" onBackPress={handleGoBack} />

      {/* Scrollable Horizontal Tab Navigation */}
      <View
        style={{
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(15),
        }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            style={[
              styles.tabContainer,
              {
                borderBottomColor: colors.bgsoft,
              },
            ]}
          >
            {tabs.map((item, index) => {
              const isSelected = activeIndex === index;
              return (
                <TouchableOpacity
                  key={item.id}
                  activeOpacity={1}
                  onPress={() => setActiveIndex(index)}
                  style={[
                    styles.tab,
                    {
                      borderColor: isSelected
                        ? colors.primaryBase
                        : colors.bgsoft,
                      borderBottomWidth: isSelected ? 1.4 : 1,
                    },
                  ]}
                >
                  <View style={styles.tabRow}>
                    <CustomIcon
                      Icon={
                        isSelected ? item.selectedIcon : item.unselectedIcon
                      }
                      height={20}
                      width={20}
                    />
                    <Text
                      style={[
                        styles.tabText,
                        isSelected && styles.selectedTabText,
                      ]}
                    >
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>

      {/* Render Tab Content */}
      <View
        style={{
          paddingHorizontal: moderateScale(10),
          width: "100%",
          flex: 1,
        }}
      >
        {renderTabContent()}
      </View>

      <TouchableOpacity
        style={[
          styles.nextbtn,
          {
            opacity: isCurrentStepValid() ? 1 : 0.5,
          },
        ]}
        disabled={!isCurrentStepValid()}
        onPress={() => {
          if (activeIndex < tabs.length - 1) {
            setActiveIndex(activeIndex + 1);
          } else {
            // All steps are completed and validated
            if (Object.values(stepValidation).every((valid) => valid)) {
              // Save all business data to Redux
              dispatch(
                updateBusinessDetails({
                  businessName,
                  description,
                  phoneNumber,
                  countryCode,
                  callingCode,
                  websiteUrl,
                })
              );

              // Mark business setup as complete
              dispatch(setSetupComplete(true));
              dispatch(setIsbusineesSetup(true));

              navigation.goBack();
            } else {
              // Find the first invalid step and navigate to it
              const invalidStepIndex = Object.entries(stepValidation).findIndex(
                ([_, isValid]) => !isValid
              );

              if (invalidStepIndex >= 0) {
                setActiveIndex(invalidStepIndex);
              }
            }
          }
        }}
      >
        <Text style={styles.nextBtnText}>
          {activeIndex === tabs.length - 1 ? "Save Business" : "Next"}
        </Text>
        {activeIndex !== tabs.length - 1 && (
          <CustomIcon Icon={ICONS.WhiteArrow} height={12} width={12} />
        )}
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default BusinessDetail;
