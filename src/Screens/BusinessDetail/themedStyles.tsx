import { StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    main: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      paddingHorizontal: moderateScale(10),
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "500",
      color: colors.text,
    },
    bottomline: {
      width: "100%",
      borderWidth: 0.5,
      borderColor: colors.primaryBase,
      marginVertical: verticalScale(10),
      alignSelf: "center",
      borderRadius: 1,
    },
    nextbtn: {
      backgroundColor: isDarkMode ? colors.primaryBase : colors.maintext,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(20),
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      justifyContent: "center",
      borderRadius: 36,
      alignSelf: "center",
    },
    nextBtnText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    tabContainer: {
      flexDirection: "row",
      borderBottomWidth: 1,
      borderBottomColor: colors.bgsoft,
    },
    tab: {
      alignItems: "center",
      paddingRight: moderateScale(10),
      paddingVertical: 10,
    },
    tabText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    selectedTabText: {
      color: colors.primaryBase,
    },
    tabRow: {
      flexDirection: "row",
      gap: moderateScale(5),
    },
  });
};
