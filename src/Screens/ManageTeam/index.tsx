import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ManageTeamProps } from "../../Navigation/Typings";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const TeamOptionItem = ({ icon, title, description, onPress }: any) => {
  const styles = useThemedStyles();

  return (
    <TouchableOpacity style={styles.optionItem} onPress={onPress}>
      <View style={styles.optionContent}>
        <CustomIcon Icon={icon} height={20} width={20} />
        <View style={{ flex: 1 }}>
          <Text style={styles.optionTitle}>{title}</Text>
          <Text style={styles.optionDescription}>{description}</Text>
        </View>
      </View>
      <CustomIcon Icon={ICONS.ArrowRightIcon} height={12} width={12} />
    </TouchableOpacity>
  );
};

const ManageTeam = ({ navigation }: ManageTeamProps) => {
  const handleGoBack = () => navigation.goBack();
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  return (
    <SafeAreaView style={styles.container}>
      {/* Unified Header */}
      <UnifiedHeader title="Team" onBackPress={handleGoBack} />

      {/* Team Options */}
      <View
        style={[
          styles.optionContainer,
          { marginTop: verticalScale(20), gap: verticalScale(5) },
        ]}
      >
        <TeamOptionItem
          icon={ICONS.ServicesTeam}
          title="Team members"
          description="Manage your team members and their roles."
          onPress={() => {
            navigation.navigate("manageTeamMember");
          }}
        />
        <View style={styles.divider} />
        <TeamOptionItem
          icon={ICONS.PermissionLock}
          title="Permissions"
          description="Manage team member permissions and roles."
          onPress={() => {
            navigation.navigate("managePermissions");
          }}
        />
      </View>

      {/* Schedule Options */}
      {/* <View style={styles.optionContainer}>
        <TeamOptionItem
          icon={ICONS.ScheduledShifts}
          title="Scheduled shifts"
          onPress={() => {}}
        />
        <TeamOptionItem
          icon={ICONS.TimesOff}
          title="Time off"
          onPress={() => {}}
        />
        <TeamOptionItem
          icon={ICONS.blockTime}
          title="Blocked time"
          onPress={() => {}}
        />
      </View> */}
    </SafeAreaView>
  );
};

export default ManageTeam;
