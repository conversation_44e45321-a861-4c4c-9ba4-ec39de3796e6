import { StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    optionContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: moderateScale(10),
      borderRadius: 16,
      marginVertical: verticalScale(10),
      paddingHorizontal: verticalScale(10),
    },
    optionItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(10),
    },
    optionContent: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
    },
    optionTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    optionDescription: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
    divider: {
      height: 1,
      width: "100%",
      backgroundColor: colors.border,
    },
  });
};
