import React, { useEffect, useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { addCategory } from "../../Redux/slices/categorySlice"; // Import the action
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { moderateScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

interface NewCategoryProps {
  navigation: any;
  route: {
    params?: {
      categoryToEdit: {
        id: string;
        name: string;
        description: string;
      } | null;
    };
  };
}

const NewCategory = ({ navigation, route }: NewCategoryProps) => {
  const dispatch = useAppDispatch();
  const { categories } = useAppSelector((state) => state.categories);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const categoryToEdit = route.params?.categoryToEdit || null;
  const isEditing = !!categoryToEdit;

  const [description, setDescription] = useState(
    categoryToEdit?.description || ""
  );
  const [categoryName, setCategoryName] = useState(categoryToEdit?.name || "");
  const [charCount, setCharCount] = useState(
    categoryToEdit?.description?.length || 0
  );

  useEffect(() => {
    setCharCount(description.length);
  }, [description]);

  const handleSaveCategory = () => {
    if (categoryName.trim() !== "") {
      const category = {
        id: isEditing ? categoryToEdit.id : String(Date.now()), // Use existing ID when editing
        name: categoryName,
        description: description,
      };

      dispatch(addCategory(category));

      Toast.show({
        type: "success",
        text1: isEditing
          ? "Category updated successfully."
          : "Category added successfully.",
      });

      navigation.goBack(); // Navigate to the categories list
    } else {
      // Show an error message if the category name is empty
      Toast.show({
        type: "error",
        text1: "Category name cannot be empty.",
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flex: 1, gap: moderateScale(20) }}
      >
        {/* Header */}
        <UnifiedHeader
          title={isEditing ? "Edit Category" : "Add Category"}
          onBackPress={() => navigation.goBack()}
        />

        <View style={{ paddingHorizontal: moderateScale(10) }}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              Category name <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={styles.input}
              placeholder="e.g Face Treatment"
              placeholderTextColor={colors.greyText}
              value={categoryName}
              onChangeText={setCategoryName}
              keyboardType="default"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Explain your category in more detail..."
              placeholderTextColor={colors.greyText}
              multiline
              maxLength={200}
              value={description}
              onChangeText={setDescription}
            />
            <View style={styles.resizetext}>
              <Text style={styles.charCount}>{description.length}/200</Text>
              <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
            </View>
          </View>
          <TouchableOpacity
            style={[
              styles.nextbtn,
              {
                opacity: !categoryName.trim() || !description.trim() ? 0.5 : 1,
              },
            ]}
            activeOpacity={0.8}
            onPress={handleSaveCategory}
            disabled={!categoryName.trim() || !description.trim()}
          >
            <Text style={styles.nextBtnText}>
              {isEditing ? "Update Category" : "Add Category"}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default NewCategory;
