import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      marginBottom: verticalScale(20),
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 5,
      marginBottom: moderateScale(10),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: "700",
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: "red",
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      marginHorizontal: moderateScale(10),
      marginVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      color: colors.text,
    },
    textArea: {
      height: 98,
      textAlignVertical: "top",
    },
    resizetext: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      paddingVertical: verticalScale(10),
      marginRight: moderateScale(10),
      gap: moderateScale(5),
    },
    charCount: {
      fontSize: 12,
      fontWeight: "500",
      textAlign: "right",
      color: colors.text,
    },
    nextbtn: {
      backgroundColor: isDarkMode ? colors.primaryBase : colors.maintext,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(20),
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      justifyContent: "center",
      borderRadius: 36,
    },
    nextBtnText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
  });
};
