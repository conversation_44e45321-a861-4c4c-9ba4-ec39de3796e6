import React, { useEffect, useState } from "react";
import {
  Image,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Country, CountryCode } from "react-native-country-picker-modal";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { updateBusinessDetails } from "../../../Redux/slices/businessSlice";
import { RootState } from "../../../Redux/store";
import { ThemedPhonePicker } from "../../../Utilities/Components/ThemedHelpers";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useThemedStep1Styles } from "./themedStyles";
import UploadImageOptions from "../../../Utilities/Components/Modal/UploadImageOptions";
import {
  Asset,
  launchCamera,
  launchImageLibrary,
} from "react-native-image-picker";
import { showErrorToast } from "../../../Utilities/toastUtils";
import Toast from "react-native-toast-message";
import { postData } from "../../../Services/ApiService";
import ENDPOINTS from "../../../Services/EndPoints";

const Step1 = ({
  onContinue,
  businessName,
  setBusinessName,
  businessEmail,
  setBusinessEmail,
  description,
  setDescription,
  phoneNumber,
  setPhoneNumber,
  countryCode,
  setCountryCode,
  callingCode,
  setCallingCode,
  websiteUrl,
  setWebsiteUrl,
  busineessLogo,
  setBusinessLogo,
}: {
  onContinue: () => void;
  businessName: string;
  setBusinessName: (value: string) => void;
  businessEmail: string;
  setBusinessEmail: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  phoneNumber: string;
  setPhoneNumber: (value: string) => void;
  countryCode: CountryCode;
  setCountryCode: (value: CountryCode) => void;
  callingCode: string;
  setCallingCode: (value: string) => void;
  websiteUrl: string;
  setWebsiteUrl: (value: string) => void;
  busineessLogo: Asset | null;
  setBusinessLogo: (value: Asset | null) => void;
}) => {
  const dispatch = useDispatch();

  const [countryVisible, setCountryVisible] = useState(false);

  const [isButtonEnabled, setIsButtonEnabled] = useState(false);

  const [image, setImage] = useState<any>(null);

  const [isModalVisible, setIsModalVisible] = useState(false);

  // Get theme context
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStep1Styles();
  const themedCommonStyles = useThemedCommonStyles();

  const onClickCapture = async () => {
    setIsModalVisible(true);
  };

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode ? country.callingCode[0] : "1");
  };

  const openPicker = () => {
    setCountryVisible(!countryVisible);
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];

        setBusinessLogo(asset);
      } else {
        console.log("No image selected or unexpected response:", response);
      }
      setIsModalVisible(false);
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        setBusinessLogo(asset);
      } else {
        console.log("No image captured or unexpected response:", result);
      }
      setIsModalVisible(false);
    } catch (error) {
      console.log("Camera capture failed:", error);
      showErrorToast("Camera Error", "Failed to capture photo");
    }
  };

  useEffect(() => {
    setIsButtonEnabled(businessName.trim() !== "" && phoneNumber.trim() !== "");
  }, [businessName, phoneNumber]);

  return (
    <KeyboardAwareScrollView
      bounces={false}
      showsVerticalScrollIndicator={false}
      style={styles.container}
    >
      <View style={styles.uploadContainer}>
        <TouchableOpacity
          onPress={onClickCapture}
          style={styles.detailiconContainer}
        >
          {busineessLogo ? (
            <Image
              source={{ uri: busineessLogo.uri }}
              style={{ height: 90, width: 90, borderRadius: 100 }}
            />
          ) : (
            <CustomIcon Icon={ICONS.DetailsStepIcon} height={30} width={30} />
          )}
          <View style={{ position: "absolute", bottom: -5, right: -5 }}>
            <CustomIcon
              Icon={image ? ICONS.EditServices : ICONS.CameraIcon}
              height={40}
              width={40}
            />
          </View>
        </TouchableOpacity>
        <Text style={styles.uploadText}>Upload your logo</Text>
      </View>
      {/* Business Name */}
      <Text style={styles.labelText}>
        Business name<Text style={{ color: colors.stateerrorbase }}>*</Text>
      </Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Enter your business name"
          placeholderTextColor={colors.greyText}
          value={businessName}
          onChangeText={setBusinessName}
        />
      </View>

      <Text style={styles.labelText}>businesss Email</Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.input]}
          textAlignVertical="top"
          placeholder="Enter your business email"
          placeholderTextColor={colors.greyText}
          value={businessEmail}
          onChangeText={setBusinessEmail}
          multiline
          numberOfLines={11}
        />
      </View>

      {/* Description */}
      <Text style={styles.labelText}>Description</Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.input,
            {
              minHeight: 200,
            },
          ]}
          textAlignVertical="top"
          placeholder="Enter a brief description of your business (e.g., services offered, specialty, etc.)"
          placeholderTextColor={colors.greyText}
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={11}
        />
      </View>

      {/* Phone Number */}
      <Text style={styles.labelText}>
        Phone Number<Text style={{ color: colors.stateerrorbase }}>*</Text>
      </Text>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginTop: verticalScale(5),
        }}
      >
        <View style={styles.phoneContainer}>
          <ThemedPhonePicker
            visible={countryVisible}
            countryCode={countryCode}
            onSelect={onSelect}
            onPress={openPicker}
          />
        </View>
        <View style={styles.phoneCountry}>
          <TextInput
            style={styles.input}
            placeholder="00 00 0000"
            placeholderTextColor={colors.greyText}
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            keyboardType="phone-pad"
            maxLength={10}
          />
        </View>
      </View>

      {/* Website */}
      <View style={{ marginTop: moderateScaleVertical(15) }}>
        <Text style={styles.labelText}>Website</Text>
        <View
          style={[
            styles.inputContainer,
            { flexDirection: "row", alignItems: "center" },
          ]}
        >
          <CustomIcon Icon={ICONS.WebsiteUrlIcon} width={16} height={16} />
          <TextInput
            style={[styles.input, { flex: 1 }]}
            placeholder="https://example.com"
            placeholderTextColor={colors.greyText}
            value={websiteUrl}
            onChangeText={(text) => setWebsiteUrl(text)}
            autoCapitalize="none"
            autoCorrect={false}
          />
        </View>
      </View>
      <TouchableOpacity
        style={[
          styles.loginButton,
          {
            backgroundColor: isButtonEnabled
              ? isDarkMode
                ? colors.primaryBase
                : colors.bgblack
              : colors.bgsoft,
          },
        ]}
        disabled={!isButtonEnabled}
        onPress={() => {
          // Save business details to Redux
          dispatch(
            updateBusinessDetails({
              businessName,
              description,
              phoneNumber,
              countryCode,
              callingCode,
              websiteUrl,
              businessLogo: busineessLogo,
            })
          );
          onContinue();
        }}
        activeOpacity={0.8}
      >
        <Text
          style={[
            styles.loginText,
            {
              color: isButtonEnabled ? colors.white : colors.greyText,
            },
          ]}
        >
          Continue
        </Text>
      </TouchableOpacity>
      <UploadImageOptions
        isModalVisible={isModalVisible}
        closeModal={() => setIsModalVisible(false)}
        onPressCamera={handleCameraPick}
        onPressGallery={handleImagePick}
      />
    </KeyboardAwareScrollView>
  );
};

export default Step1;
