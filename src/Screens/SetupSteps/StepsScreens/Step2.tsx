import React, { useEffect, useState } from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { useDispatch } from "react-redux";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { updateSelectedServices } from "../../../Redux/slices/businessSlice";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedStep2Styles } from "./themedStyles";
import { fetchData } from "../../../Services/ApiService";
import ENDPOINTS from "../../../Services/EndPoints";
import {
  Category,
  GetAllCategoriesApiResponse,
} from "../../../Services/ApiResponse";
import Toast from "react-native-toast-message";

const Step2 = ({
  onContinue,
  selectedItems,
  setSelectedItems,
}: {
  onContinue: () => void;
  selectedItems: string[];
  setSelectedItems: (value: string[]) => void;
}) => {
  const dispatch = useDispatch();

  const [globalCategories, setGlobalCategories] = useState<Category[]>([]);

  // Get theme context
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStep2Styles();

  const toggleSelection = (item: Category) => {
    const newSelectedItems = selectedItems.includes(item._id)
      ? selectedItems.filter((id) => id !== item._id)
      : [...selectedItems, item._id];
    setSelectedItems(newSelectedItems);
  };

  const renderItem = ({ item }: { item: Category }) => {
    const isSelected = selectedItems.includes(item._id);

    return (
      <TouchableOpacity
        style={[
          styles.outerview,
          {
            backgroundColor: isSelected
              ? colors.primaryBase
              : colors.cardBackground,
          },
        ]}
        onPress={() => toggleSelection(item)}
        activeOpacity={0.8}
      >
        <View style={styles.iconAndLabel}>
          <CustomIcon
            Icon={ICONS[item.icon as keyof typeof ICONS] || ICONS.ReminderIcon}
            height={32}
            width={25}
          />
          <Text
            style={[
              styles.label,
              { color: isSelected ? colors.white : colors.text },
            ]}
          >
            {item.name}
          </Text>
        </View>
        <Text
          style={[
            styles.description,
            { color: isSelected ? colors.white : colors.greyText },
          ]}
        >
          {item.description}
        </Text>
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    const getGlobalCategories = async () => {
      try {
        const response = await fetchData<GetAllCategoriesApiResponse>(
          ENDPOINTS.getAllCategory
        );
        if (response.data.success) {
          setGlobalCategories(response.data.data.categories);
        }
      } catch (error: any) {
        console.error("Error fetching global categories:", error);
        Toast.show({
          type: "error",
          text1: "Something went wrong. Please try again later.",
        });
      }
    };

    getGlobalCategories();
  }, []);

  return (
    <View style={styles.container}>
      <FlatList
        data={globalCategories}
        numColumns={2}
        keyExtractor={(item) => item._id}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        columnWrapperStyle={styles.columnWrapper}
        contentContainerStyle={styles.contentContainer}
      />

      <TouchableOpacity
        style={[
          styles.continueButton,
          {
            backgroundColor:
              selectedItems.length > 0
                ? isDarkMode
                  ? colors.primaryBase
                  : colors.maintext
                : colors.bgsoft,
          },
        ]}
        activeOpacity={0.8}
        onPress={() => {
          // Save selected services to Redux
          dispatch(updateSelectedServices(selectedItems));
          onContinue();
        }}
        disabled={selectedItems.length === 0}
      >
        <Text
          style={[
            styles.continueText,
            {
              color: selectedItems.length > 0 ? colors.white : colors.greyText,
            },
          ]}
        >
          Save
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default Step2;
