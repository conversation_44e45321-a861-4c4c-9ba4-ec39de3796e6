import { StyleSheet, Platform } from 'react-native';
import { moderateScale, moderateScaleVertical, verticalScale } from '../../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../../Utilities/ThemeContext';

export const useThemedStep1Styles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
    },
    detailiconContainer: {
      width: moderateScale(90),
      height: moderateScale(90),
      borderWidth: 2,
      borderColor: isDarkMode ? colors.cardBackground : colors.white,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 66,
      marginBottom: 20,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    input: {
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    phoneCountry: {
      borderWidth: 1,
      borderColor: colors.border,
      borderTopRightRadius: 12,
      borderBottomRightRadius: 12,
      width: "76.5%",
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    phoneContainer: {
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginTop: verticalScale(10),
      marginBottom: 20,
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    labelText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    uploadContainer: {
      alignItems: "center",
      marginBottom: verticalScale(20),
    },
    uploadText: {
      color: colors.text,
      fontSize: 12,
      fontWeight: "400",
    },
  });
};

export const useThemedStep2Styles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      justifyContent: "space-between",
      flex: 1,
    },
    outerview: {
      flex: 1,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      paddingVertical: moderateScaleVertical(10),
      paddingHorizontal: moderateScale(10),
      gap: verticalScale(10),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    iconAndLabel: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
    },
    label: {
      flex: 1,
      color: colors.text,
    },
    description: {
      marginTop: verticalScale(2),
      color: colors.greyText,
    },
    columnWrapper: {
      gap: moderateScale(10),
      marginBottom: moderateScaleVertical(10),
    },
    contentContainer: {
      paddingBottom: verticalScale(20),
    },
    continueButton: {
      backgroundColor: colors.bgsoft,
      borderRadius: 12,
      paddingVertical: moderateScaleVertical(12),
      alignItems: "center",
      justifyContent: "center",
    },
    continueText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
  });
};

export const useThemedStep3Styles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 12,
      flex: 1,
      paddingBottom: verticalScale(10),
    },
    image: {
      width: "100%",
      height: 100,
    },
    sharelinkcon: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      marginTop: 15,
    },
    copyicon: {
      borderLeftWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: 10,
      paddingVertical: 10,
    },
    linkouterview: {
      paddingHorizontal: 10,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      marginTop: moderateScaleVertical(15),
      flexDirection: "row",
      gap: moderateScale(10),
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    loginButton: {
      borderRadius: 12,
      paddingVertical: moderateScaleVertical(12),
      alignItems: "center",
      justifyContent: "center",
      width: "90%",
      alignSelf: "center",
      backgroundColor: isDarkMode ? colors.primaryBase : colors.bgblack,
    },
    loginText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.white,
    },
    titleText: {
      color: colors.text,
      fontSize: 30,
      fontWeight: "900",
      textAlign: "center",
    },
    subtitleText: {
      color: colors.greyText,
      fontSize: 16,
      fontWeight: "400",
      textAlign: "center",
      marginTop: verticalScale(2),
    },
    linkText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "400",
    },
  });
};
