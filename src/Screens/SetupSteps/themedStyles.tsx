import { StyleSheet } from 'react-native';
import { moderateScale, moderateScaleVertical, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1, 
      padding: 20,
      backgroundColor: colors.background,
    },
    stepIndicatorContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: verticalScale(10),
    },
    stepWrapper: {
      flex: 1,
    },
    stepText: {
      fontSize: 12,
      fontWeight: '700',
      textTransform: 'uppercase',
      marginTop: moderateScaleVertical(5),
    },
    activeStepText: {
      color: colors.stateerrorbase,
    },
    inactiveStepText: {
      color: colors.greyText,
    },
    underline: {
      height: 3,
      width: '90%',
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: moderateScale(20),
      gap: moderateScale(15),
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: 'center',
      marginTop: verticalScale(10),
      marginBottom: 20,
    },
    loginText: { 
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    titleText: {
      color: colors.text,
      fontSize: 20,
      fontWeight: '500',
    },
    emptyText: {
      textAlign: 'center',
      color: colors.text,
    },
  });
};
