import { StyleSheet, Platform } from "react-native";
import {
  moderateScale,
  moderateScaleVertical,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeAreaContainer: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
    },
    centeredView: {
      alignItems: "center",
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      padding: Platform.OS == "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 10,
      width: "100%",
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    input: {
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginTop: moderateScaleVertical(10),
      marginBottom: 20,
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    passwordmatchingline: {
      width: 95,
      height: 4,
      backgroundColor: colors.stateerrorbase,
      borderRadius: 1.2,
    },
    passwordmatchingline1: {
      width: 95,
      height: 4,
      backgroundColor: colors.statewarningbase,
      borderRadius: 1.2,
      marginHorizontal: 10,
    },
    passwordmatchingline2: {
      width: 95,
      height: 4,
      backgroundColor: colors.statesuccessbase,
      borderRadius: 1.2,
    },
    warningContainer: {
      flexDirection: "row",
      gap: moderateScale(5),
      alignItems: "center",
    },
    titleText: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "500",
      textAlign: "center",
    },
    subtitleText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "400",
    },
    labelText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    footerContainer: {
      alignItems: "center",
    },
    footerText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    linkTextPrimary: {
      color: colors.primaryBase,
      fontSize: 14,
      fontWeight: "500",
      textDecorationLine: "underline",
    },
    validationText: {
      marginTop: 5,
      fontSize: 12,
      fontWeight: "400",
      color: colors.text,
    },
    warningText: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
  });
};
