import React, { useEffect, useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { UpdatePasswordScreenProps } from "../../Navigation/Typings";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";

const SetNewPassword = ({ navigation, route }: UpdatePasswordScreenProps) => {
  const { email, resetToken } = route.params;
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [secureText, setSecureText] = useState(true);
  const [secureConfirmText, setSecureConfirmText] = useState(true);

  const [hasUppercase, setHasUppercase] = useState(false);
  const [hasNumber, setHasNumber] = useState(false);
  const [hasMinLength, setHasMinLength] = useState(false);

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const isButtonEnabled = password.length > 0 && confirmPassword.length > 0;

  useEffect(() => {
    setHasUppercase(/[A-Z]/.test(password));
    setHasNumber(/\d/.test(password));
    setHasMinLength(password.length >= 8);
  }, [password]);

  const handleResetPassword = async () => {
    if (password !== confirmPassword) {
      Toast.show({
        type: "error",
        text1: "Passwords do not match",
      });
      return;
    }

    const response = await postData(ENDPOINTS.updatePassword, {
      email,
      resetToken,
      newPassword: password,
    });

    if (response.data.success) {
      Toast.show({
        type: "success",
        text1: response.data.message,
      });

      navigation.replace("LoginScreen");
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView
        style={{
          flex: 1,
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(20),
        }}
      >
        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.centeredView}>
            <CustomIcon Icon={ICONS.NewPasswordIcon} height={96} width={96} />
            <Text
              style={[
                themedCommonStyles.font24W500,
                { marginTop: verticalScale(10) },
              ]}
            >
              New Password
            </Text>
            <Text
              style={[
                themedCommonStyles.font16400,
                { marginTop: verticalScale(2) },
              ]}
            >
              Type your new password
            </Text>
          </View>

          <View style={{ marginTop: moderateScaleVertical(20) }}>
            <Text style={styles.labelText}>
              New Password
              <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={secureText}
                value={password}
                onChangeText={setPassword}
              />
              <TouchableOpacity onPress={() => setSecureText(!secureText)}>
                <CustomIcon
                  Icon={secureText ? ICONS.EyeoffIcon : ICONS.EyeIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View>
            <Text style={styles.labelText}>
              Repeat Password
              <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={secureConfirmText}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
              />
              <TouchableOpacity
                onPress={() => setSecureConfirmText(!secureConfirmText)}
              >
                <CustomIcon
                  Icon={secureConfirmText ? ICONS.EyeoffIcon : ICONS.EyeIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.warningContainer}>
            <CustomIcon Icon={ICONS.WarningIcon} height={12} width={12} />
            <Text style={styles.warningText}>
              Must contain at least 1 uppercase letter, 1 number, min. 8
              characters.
            </Text>
          </View>

          {password.length > 0 && (
            <View
              style={{
                paddingVertical: verticalScale(10),
                flexDirection: "row",
              }}
            >
              <View
                style={[
                  styles.passwordmatchingline,
                  {
                    backgroundColor: hasUppercase
                      ? colors.stateerrorbase
                      : colors.bgsoft,
                  },
                ]}
              />
              <View
                style={[
                  styles.passwordmatchingline1,
                  {
                    backgroundColor: hasNumber
                      ? colors.statewarningbase
                      : colors.bgsoft,
                  },
                ]}
              />
              <View
                style={[
                  styles.passwordmatchingline2,
                  {
                    backgroundColor: hasMinLength
                      ? colors.statesuccessbase
                      : colors.bgsoft,
                  },
                ]}
              />
            </View>
          )}

          {password.length > 0 && (
            <View>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasUppercase ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 1 uppercase
              </Text>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasNumber ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 1 number
              </Text>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasMinLength ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 8 characters
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.loginButton,
              {
                backgroundColor: isButtonEnabled
                  ? colors.primaryBase
                  : colors.bgsoft,
              },
            ]}
            disabled={!isButtonEnabled}
            onPress={handleResetPassword}
            activeOpacity={0.8}
          >
            <Text
              style={[
                styles.loginText,
                { color: isButtonEnabled ? colors.white : colors.greyText },
              ]}
            >
              Reset Password
            </Text>
          </TouchableOpacity>
        </KeyboardAwareScrollView>
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            Changed your mind?{` `}
            <Text
              style={styles.linkTextPrimary}
              onPress={() => {
                navigation.navigate("LoginScreen");
              }}
            >
              Go back
            </Text>
          </Text>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default SetNewPassword;
