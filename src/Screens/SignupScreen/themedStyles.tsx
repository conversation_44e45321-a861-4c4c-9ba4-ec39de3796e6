import { StyleSheet, Platform } from 'react-native';
import { moderateScale, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeAreaContainer: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
    },
    centeredView: {
      alignItems: "center",
    },
    container: {
      justifyContent: "center",
      marginTop: 50,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      padding: Platform.OS == "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 10,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    phoneCountry: {
      borderWidth: 1,
      borderColor: colors.border,
      borderTopRightRadius: 10,
      borderBottomRightRadius: 10,
      width: "75%",
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    input: {
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    warningContainer: {
      flexDirection: "row",
      gap: moderateScale(5),
      alignItems: "center",
    },
    phoneLabel: {
      color: colors.text,
      marginTop: verticalScale(20),
    },
    pickerContainer: {
      marginLeft: moderateScale(5),
    },
    checkboxContainer: {
      flexDirection: "row",
      marginVertical: verticalScale(20),
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: 2,
      borderColor: colors.border,
      backgroundColor: isDarkMode ? "transparent" : colors.white,
      borderRadius: 4,
      marginRight: 8,
      marginTop: 3,
    },
    text: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
      width: "80%",
    },
    linkText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
      textDecorationLine: "underline",
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginTop: verticalScale(10),
      marginBottom: 20,
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    passwordmatchingline: {
      width: 95,
      height: 4,
      backgroundColor: colors.stateerrorbase,
      borderRadius: 1.2,
    },
    passwordmatchingline1: {
      width: 95,
      height: 4,
      backgroundColor: colors.statewarningbase,
      borderRadius: 1.2,
      marginHorizontal: 10,
    },
    passwordmatchingline2: {
      width: 95,
      height: 4,
      backgroundColor: colors.statesuccessbase,
      borderRadius: 1.2,
    },
    titleText: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "500",
      textAlign: "center",
    },
    subtitleText: {
      color: colors.greyText,
      fontSize: 14,
      fontWeight: "400",
      textAlign: "center",
    },
    labelText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    footerContainer: {
      alignItems: "center",
      marginTop: verticalScale(20),
    },
    footerText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    linkTextPrimary: {
      color: colors.primaryBase,
      fontSize: 14,
      fontWeight: "500",
      textDecorationLine: "underline",
    },
    validationText: {
      marginTop: 5,
      fontSize: 12,
      fontWeight: "400",
      color: colors.text,
    },
    warningText: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
    phoneContainer: {
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
      borderTopLeftRadius: 12,
      borderBottomLeftRadius: 12,
      borderColor: colors.border,
      borderLeftWidth: 1,
      borderTopWidth: 1,
      borderBottomWidth: 1,
    },
    profileImageContainer: {
      alignItems: "center",
      marginTop: verticalScale(20),
    },
    profileImageWrapper: {
      width: 70,
      height: 70,
      borderRadius: 50,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
      position: "relative",
      borderWidth: 1,
      borderColor: colors.border,
    },
    profileImage: {
      width: 70,
      height: 70,
      borderRadius: 50,
    },
    profileImagePlaceholder: {
      width: 70,
      height: 70,
      borderRadius: 50,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
    },
    cameraIconContainer: {
      position: "absolute",
      bottom: 0,
      right: 0,
      backgroundColor: colors.primaryBase,
      width: 25,
      height: 25,
      borderRadius: 15,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 2,
      borderColor: isDarkMode ? colors.background : colors.white,
    },
    uploadPhotoText: {
      marginTop: verticalScale(10),
      fontSize: 14,
      fontWeight: "500",
    },
  });
};
