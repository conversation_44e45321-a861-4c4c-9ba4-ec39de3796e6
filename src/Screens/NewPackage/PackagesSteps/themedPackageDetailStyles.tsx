import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedPackageDetailStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 5,
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: "red",
    },
    input: {
      fontSize: 14,
      fontWeight: '400',
      marginHorizontal: moderateScale(10),
      marginVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      color: colors.text,
    },
    textArea: {
      height: 98,
      textAlignVertical: "top",
    },
    resizetext: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      paddingVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      marginRight: moderateScale(10),
      gap: moderateScale(3),
    },
    charCount: {
      fontSize: 12,
      fontWeight: '400',
      textAlign: "right",
      color: colors.text,
    },
    categorySelector: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: moderateScale(10),
      paddingVertical: Platform.OS === "ios" ? verticalScale(10) : verticalScale(5),
    },
    modalContainer: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: {
      width: "80%",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      padding: 20,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 20,
    },
    categoryItem: {
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
  });
};
