import { StyleSheet } from "react-native";
import { verticalScale } from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedPackageServicesStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    item: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: 10,
      borderRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 10,
      borderWidth: 1,
      borderColor: colors.border,
    },
    serviceName: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    serviceDetails: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.greyText,
    },
    addServiceButton: {
      borderWidth: 1,
      borderRadius: 10,
      borderColor: colors.border,
      marginTop: verticalScale(20),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(10),
      alignSelf: "center",
    },
    addServiceText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
    },
    emptyText: {
      fontSize: 14,
      fontWeight: '400',
      textAlign: "center",
      color: colors.text,
      marginVertical: 20,
    },
  });
};
