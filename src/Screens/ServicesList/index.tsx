import React, { useRef, useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import DraggableFlatList from "react-native-draggable-flatlist"; // Make sure you have react-native-gesture-handler installed and configured as per previous instructions
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ServiceListProps } from "../../Navigation/Typings";
import {
  deletePackage,
  reorderPackages,
  selectPackagesFlat,
} from "../../Redux/slices/packagesSlice";
import {
  deleteService,
  reorderCategories, // <--- Import reorderCategories
  selectServicesByCategory,
} from "../../Redux/slices/servicesSlice";
import { getCurrencySymbol } from "../../Utilities/currencyUtils";
import { moderateScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const ServicesList = ({ navigation }: ServiceListProps) => {
  const dispatch = useDispatch();
  const [search, setSearch] = useState("");
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"services" | "packages">(
    "services"
  );

  const servicesByCategory = useSelector(selectServicesByCategory);
  const packages = useSelector(selectPackagesFlat);
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();

  // Filter services based on search term
  const getFilteredServicesData = () => {
    if (!search) return servicesByCategory;

    const searchLower = search.toLowerCase();
    // When filtering, return a NEW array so DraggableFlatList can work with it
    return servicesByCategory
      .map((category) => ({
        ...category,
        services: category.services.filter(
          (service) =>
            service.businessName.toLowerCase().includes(searchLower) ||
            service.duration.toLowerCase().includes(searchLower)
        ),
      }))
      .filter((category) => category.services.length > 0);
  };

  // Filter packages based on search term
  const getFilteredPackagesData = () => {
    if (!search) return packages;

    const searchLower = search.toLowerCase();
    return packages.filter(
      (pkg) =>
        pkg.packageName.toLowerCase().includes(searchLower) ||
        pkg.duration.toLowerCase().includes(searchLower)
    );
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  const toggleCategory = (category: string) => {
    setExpandedCategory((prevCategory) =>
      prevCategory === category ? null : category
    );
  };

  // Handle delete actions
  const handleDeleteService = (serviceId: string) => {
    dispatch(deleteService(serviceId));
  };

  const handleDeletePackage = (packageId: string) => {
    dispatch(deletePackage(packageId));
  };

  const handleCategoryDragEnd = ({ data }: { data: any[] }) => {
    dispatch(reorderCategories(data));
  };

  const handlePackageDragEnd = ({ data }: { data: any[] }) => {
    dispatch(reorderPackages(data));
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Unified Header */}
      <UnifiedHeader
        title={
          activeTab === "services" ? "List of services" : "List of packages"
        }
        onBackPress={handleGoBack}
        rightButtonText={
          activeTab === "services" ? "Add Service" : "Add Package"
        }
        onRightButtonPress={() => {
          if (activeTab === "services") {
            navigation.navigate("addNewService", { service: null });
          } else {
            navigation.navigate("addNewPackage", {});
          }
        }}
      />

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "services" && styles.activeTab]}
          onPress={() => setActiveTab("services")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "services" && styles.activeTabText,
            ]}
          >
            Services
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "packages" && styles.activeTab]}
          onPress={() => setActiveTab("packages")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "packages" && styles.activeTabText,
            ]}
          >
            Packages
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.inputContainer}>
        <CustomIcon Icon={ICONS.SearchIcon} width={15} height={14.17} />
        <TextInput
          style={styles.input}
          placeholder={
            activeTab === "services"
              ? "Search by service name or duration"
              : "Search by package name or duration"
          }
          placeholderTextColor={colors.greyText}
          value={search}
          onChangeText={(text) =>
            setSearch(text.trim().length === 0 ? text.trim() : text)
          }
        />
      </View>
      {/* Services or Packages List */}
      {activeTab === "services" ? (
        <DraggableFlatList
          data={getFilteredServicesData()}
          keyExtractor={(item) => item.category}
          onDragEnd={handleCategoryDragEnd}
          renderItem={({ item, drag, isActive }) => (
            <View
              style={[
                styles.serviceContainer,
                isActive && { backgroundColor: colors.StateLightpinkColor },
              ]}
            >
              <TouchableOpacity
                onPress={() => toggleCategory(item.category)}
                style={[
                  styles.categoryHeader,
                  expandedCategory === item.category &&
                    styles.categoryHeaderExpanded,
                ]}
                activeOpacity={0.8}
              >
                <TouchableOpacity
                  onPress={() => toggleCategory(item.category)}
                  onLongPress={drag}
                  style={styles.categoryTitleContainer}
                >
                  {/* Reorder icon here, indicating the draggable handle */}
                  <CustomIcon Icon={ICONS.ReorderIcon} height={24} width={24} />
                  <Text style={styles.categoryTitle}>{item.category}</Text>
                </TouchableOpacity>
                <CustomIcon
                  Icon={
                    expandedCategory === item.category
                      ? isDarkMode
                        ? ICONS.WhiteArrowUpIcon
                        : ICONS.ArrowUpIcon
                      : isDarkMode
                      ? ICONS.WhiteArrowDownIcon
                      : ICONS.ArrowDownIcon
                  }
                  width={24}
                  height={24}
                />
              </TouchableOpacity>

              {expandedCategory === item.category && (
                <View>
                  {item.services.map((service) => (
                    <View key={service.id} style={styles.serviceItem}>
                      <View style={styles.serviceInfoContainer}>
                        <CustomIcon
                          Icon={ICONS.ReorderIcon}
                          height={24}
                          width={24}
                        />
                        <View>
                          <Text style={styles.serviceName}>
                            {service.businessName}
                          </Text>
                          <Text style={styles.serviceDetails}>
                            {getCurrencySymbol(service.currency)}
                            {service.price} · {service.duration}
                          </Text>
                        </View>
                      </View>
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: moderateScale(10),
                        }}
                      >
                        <TouchableOpacity
                          activeOpacity={0.8}
                          onPress={() => {
                            navigation.navigate("addNewService", { service });
                          }}
                        >
                          <CustomIcon
                            Icon={
                              isDarkMode
                                ? ICONS.EditServices
                                : ICONS.EditServices
                            }
                            width={40}
                            height={40}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.moreOptionsButton}
                          activeOpacity={0.8}
                          onPress={() => {
                            handleDeleteService(service.id);
                          }}
                        >
                          <CustomIcon
                            Icon={
                              isDarkMode ? ICONS.DeleteIcon : ICONS.DeleteIcon
                            }
                            width={20}
                            height={20}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <CustomIcon
                Icon={ICONS.ServiceManagementIcon}
                width={108}
                height={108}
              />
              <Text style={styles.noServiceText}>
                {search ? "No matching services" : "No services"}
              </Text>
              <Text style={styles.noServiceSubText}>
                Houston, we have a problem… no services here! Let's create the
                first one.
              </Text>
              {!search && (
                <TouchableOpacity
                  style={styles.addServiceButton}
                  activeOpacity={0.8}
                  onPress={() =>
                    navigation.navigate("addNewService", {
                      service: null,
                    })
                  }
                >
                  <CustomIcon
                    Icon={ICONS.SimplePlusIcon}
                    height={20}
                    width={20}
                  />
                  <Text style={styles.addServiceText}>
                    Add your first service
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        />
      ) : (
        <DraggableFlatList
          data={getFilteredPackagesData()}
          keyExtractor={(item) => item.id}
          onDragEnd={handlePackageDragEnd}
          renderItem={({ item, drag }) => (
            <View style={styles.packageContainer}>
              <TouchableOpacity
                onPress={() => toggleCategory(item.id)}
                style={[
                  styles.packageHeader,
                  expandedCategory === item.id && styles.packageHeaderExpanded,
                ]}
                activeOpacity={0.8}
              >
                <TouchableOpacity
                  onPress={() => toggleCategory(item.id)}
                  onLongPress={drag}
                  style={styles.packageTitleContainer}
                >
                  <CustomIcon Icon={ICONS.ReorderIcon} height={24} width={24} />
                  <View style={styles.packageTitleInfo}>
                    <Text style={styles.packageTitle}>{item.packageName}</Text>
                    <Text style={styles.packageSubtitle}>
                      {getCurrencySymbol(item.currency)}
                      {item.price} · {item.duration} · {item.services.length}{" "}
                      services
                    </Text>
                  </View>
                </TouchableOpacity>
                <View style={styles.packageHeaderRight}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => {
                      navigation.navigate("addNewPackage", {
                        packageToEdit: item,
                      });
                    }}
                  >
                    <CustomIcon
                      Icon={ICONS.EditServices}
                      width={40}
                      height={40}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.moreOptionsButton}
                    activeOpacity={0.8}
                    onPress={() => {
                      navigation.navigate("addNewPackage", {
                        packageToEdit: item,
                      });
                    }}
                  >
                    <CustomIcon
                      Icon={ICONS.DeleteIcon}
                      width={20}
                      height={20}
                    />
                  </TouchableOpacity>
                  {/* <CustomIcon
                    Icon={
                      expandedCategory === item.id
                        ? isDarkMode
                          ? ICONS.WhiteArrowUpIcon
                          : ICONS.ArrowUpIcon
                        : isDarkMode
                        ? ICONS.WhiteArrowDownIcon
                        : ICONS.ArrowDownIcon
                    }
                    width={24}
                    height={24}
                  /> */}
                </View>
              </TouchableOpacity>

              {expandedCategory === item.id && (
                <View style={styles.packageServicesContainer}>
                  <Text style={styles.packageServicesTitle}>
                    Included Services:
                  </Text>
                  {item.services.map((service) => (
                    <View key={service.id} style={styles.packageServiceItem}>
                      <View style={styles.serviceInfoContainer}>
                        <CustomIcon
                          Icon={ICONS.ReorderIcon}
                          height={20}
                          width={20}
                        />
                        <View>
                          <Text style={styles.serviceName}>
                            {service.businessName}
                          </Text>
                          <Text style={styles.serviceDetails}>
                            {getCurrencySymbol(service.currency)}
                            {service.price} · {service.duration}
                          </Text>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <CustomIcon
                Icon={ICONS.ServiceManagementIcon}
                width={108}
                height={108}
              />
              <Text style={styles.noServiceText}>
                {search ? "No matching packages" : "No packages"}
              </Text>
              <Text style={styles.noServiceSubText}>
                No packages found. Create your first package to bundle services
                together!
              </Text>
              {!search && (
                <TouchableOpacity
                  style={styles.addServiceButton}
                  activeOpacity={0.8}
                  onPress={() => navigation.navigate("addNewPackage", {})}
                >
                  <CustomIcon
                    Icon={ICONS.SimplePlusIcon}
                    height={20}
                    width={20}
                  />
                  <Text style={styles.addServiceText}>
                    Add your first package
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        />
      )}
    </SafeAreaView>
  );
};

export default ServicesList;
