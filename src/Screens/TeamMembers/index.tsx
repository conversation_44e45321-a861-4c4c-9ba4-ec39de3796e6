import React, { useState } from 'react';
import {
  FlatList,
  Image,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ICONS from '../../Assets/Icon';
import CustomIcon from '../../Components/CustomIcon';
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ManageTeamMeberProps } from '../../Navigation/Typings';
import { deleteMembers, setMembers } from '../../Redux/slices/memberSlice';
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { useThemedCommonStyles } from '../../Utilities/Styles/themedCommonStyles';
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from './themedStyles';
import { verticalScale } from "../../Utilities/Styles/responsiveSize";

const TeamMembers = ({ navigation }: ManageTeamMeberProps) => {
  const [selectAll, setSelectAll] = useState(false);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);

  const dispatch = useAppDispatch();
  const { members } = useAppSelector((state) => state.members);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const toggleCheckbox = (id: string) => {
    const updatedClients = members.map((client) =>
      client.id === id ? { ...client, checked: !client.checked } : client
    );
    dispatch(setMembers(updatedClients));

    const allChecked = updatedClients.every((client) => client.checked);
    setSelectAll(allChecked);
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    const updatedClients = members.map((client) => ({
      ...client,
      checked: newSelectAll,
    }));
    dispatch(setMembers(updatedClients));
  };

  const handleDelete = () => {
    const membersToDelete = members
      .filter((client) => client.checked)
      .map((client) => client.id);
    dispatch(deleteMembers(membersToDelete));
    setSelectAll(false);
    setIsModalVisible(false);
  };

  const filteredMembers = members.filter((client) =>
    `${client.name} ${client.email}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  const handleGoBack = () => {
    navigation.goBack();
  };

  const renderItem = ({ item, index }: any) => (
    <View
      style={[
        styles.memberItem,
        item.checked && styles.memberItemChecked,
      ]}
    >
      <TouchableOpacity
        onPress={() => toggleCheckbox(item.id)}
        style={[
          styles.checkbox,
          {
            backgroundColor: item.checked ? colors.primaryBase : 'transparent',
            borderWidth: 1,
            borderColor: item.checked ? 'transparent' : colors.border,
          },
        ]}
      >
        {item.checked && (
          <View style={styles.checkboxInner}>
            <CustomIcon
              Icon={ICONS.CheckRightIcon}
              height={12}
              width={12}
            />
          </View>
        )}
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('addTeamMember', {
            clientData: item,
          });
        }}
        style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}
      >
        <Image
          source={{ uri: item.image }}
          style={styles.profileImage}
        />
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{item.name}</Text>
          <Text style={styles.memberEmail}>{item.email}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Unified Header */}
      <UnifiedHeader
        title="Team members"
        onBackPress={handleGoBack}
        rightButtonText="Add"
        onRightButtonPress={() => {
          navigation.navigate("addTeamMember", {});
        }}
      />

      <View style={{ paddingHorizontal: verticalScale(10) }}>
        {/* Search Input */}
        <View style={styles.inputContainer}>
          <CustomIcon Icon={ICONS.SearchIcon} width={15} height={14.17} />
          <TextInput
            style={styles.input}
            placeholder="Search by name or email"
            placeholderTextColor={colors.greyText}
            value={search}
            onChangeText={setSearch}
          />
        </View>

        {/* Select All and Delete Button */}
        {members.some((client) => client.checked) && (
          <View style={styles.selectAllContainer}>
            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center" }}
              onPress={toggleSelectAll}
            >
              <View
                style={[
                  styles.checkbox,
                  {
                    backgroundColor: selectAll
                      ? colors.primaryBase
                      : "transparent",
                    borderWidth: 1,
                    borderColor: selectAll ? "transparent" : colors.border,
                  },
                ]}
              >
                {selectAll && (
                  <View style={styles.checkboxInner}>
                    <CustomIcon
                      Icon={ICONS.CheckRightIcon}
                      height={12}
                      width={12}
                    />
                  </View>
                )}
              </View>
              <Text
                style={{
                  fontSize: 12,
                  fontWeight: "400",
                  color: colors.greyText,
                }}
              >
                {members.filter((client) => client.checked).length} MEMBERS
                SELECTED
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.deletebtn}
              onPress={() => setIsModalVisible(true)}
            >
              <CustomIcon Icon={ICONS.DeleteIcon} height={20} width={20} />
              <Text style={styles.deleteText}>Delete</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Members List */}
        {members.length > 0 ? (
          <FlatList
            data={filteredMembers}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <CustomIcon Icon={ICONS.NoClientIcon} width={108} height={108} />
            <Text style={styles.noServiceText}>No team members</Text>
            <Text style={styles.noServiceSubText}>
              Add team members to manage your business together
            </Text>
          </View>
        )}
      </View>

      {/* Delete Confirmation Modal */}
      <Modal transparent={true} visible={isModalVisible} animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Delete team members</Text>
            <Text style={styles.modalText}>
              Are you sure you want to delete the selected team members? This
              action cannot be undone.
            </Text>
            <TouchableOpacity
              style={styles.confirmDeleteBtn}
              onPress={handleDelete}
            >
              <CustomIcon Icon={ICONS.WhiteDeleteIcon} width={20} height={20} />
              <Text style={styles.confirmDeleteText}>Delete</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelDeleteBtn}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.cancelDeleteText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default TeamMembers;
