import { StyleSheet } from "react-native";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerText: {
      color: colors.text,
    },
    imageBackground: {
      height: 112,
      width: "100%",
      marginVertical: verticalScale(20),
      justifyContent: "center",
    },
    profileContainer: {
      flex: 1,
      padding: 20,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    profileInfoContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
    },
    profileImageWrapper: {
      height: 60,
      width: 60,
      borderWidth: 3,
      borderColor: colors.white,
      borderRadius: 30,
      alignItems: "center",
      overflow: "hidden",
      justifyContent: "center",
    },
    profileImage: {
      height: 56,
      width: 56,
    },
    editButton: {
      backgroundColor: "rgba(82, 76, 76, 0.26)",
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      borderRadius: 20,
      paddingVertical: 7,
      paddingHorizontal: 15,
    },
    optionContainer: {
      backgroundColor: colors.cardBackground,
      paddingLeft: moderateScale(10),
      paddingRight: moderateScale(20),
      borderRadius: 16,
      marginTop: moderateScaleVertical(20),
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: moderateScaleVertical(15),
    },
    optionTextContainer: {
      flex: 1,
      marginLeft: moderateScale(10),
    },
    optionText: {
      color: colors.text,
    },
    policiesContainer: {
      backgroundColor: colors.cardBackground,
      borderRadius: 16,
      paddingVertical: moderateScaleVertical(15),
      paddingRight: moderateScale(20),
      paddingLeft: moderateScale(10),
      marginTop: 20,
    },
    policyOption: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: moderateScaleVertical(10),
    },
    policyTextContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
    },
    policyText: {
      color: colors.text,
    },
    logDeleteCon: {
      backgroundColor: colors.cardBackground,
      borderTopRightRadius: 16,
      borderTopLeftRadius: 16,
      paddingVertical: moderateScaleVertical(20),
      paddingLeft: moderateScale(10),
      paddingRight: moderateScale(10),
      marginVertical: 20,
    },
    logDeleteOption: {
      flexDirection: "row",
      gap: moderateScale(10),
      alignItems: "center",
    },
    logoutText: {
      color: colors.text,
    },
    deleteText: {
      color: colors.stateerrorbase,
    },
    mainContainer: {
      backgroundColor: colors.cardBackground,
      borderRadius: 16,
      paddingVertical: moderateScaleVertical(20),
      paddingLeft: moderateScale(10),
      paddingRight: moderateScale(20),
    },
    languagebtn: {
      flexDirection: "row",
      padding: 8,
      alignItems: "center",
      gap: moderateScale(5),
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    languageText: {
      color: colors.text,
    },
    LCAbtn: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginTop: 20,
    },
    selectionContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      paddingVertical: 6,
      paddingHorizontal: 12,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    con: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginTop: 20,
    },
  });
};
