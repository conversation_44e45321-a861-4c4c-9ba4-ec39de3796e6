import { StyleSheet } from 'react-native';
import { moderateScale, moderateScaleVertical, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(20),
      backgroundColor: isDarkMode ? colors.black : "#F5F7FA",
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    image: {
      width: "100%",
      height: 100,
    },
    profileImage: {
      height: 35,
      width: 35,
      borderRadius: 100,
    },
    addmemberbtn: {
      paddingHorizontal: 10,
      paddingVertical: 10,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      marginTop: moderateScaleVertical(15),
      alignItems: "center",
      alignSelf: "center",
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "500",
      textAlign: "center",
      color: colors.text,
    },
    optionContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingHorizontal: moderateScale(10),
      borderRadius: 12,
      marginVertical: moderateScaleVertical(10),
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: moderateScaleVertical(15),
    },
    optionTextContainer: {
      flex: 1,
      marginLeft: moderateScale(10),
    },
    optionTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    optionSubtitle: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
    row: {
      flexDirection: "row",
      paddingVertical: 20,
    },
    card: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingHorizontal: moderateScale(10),
      paddingVertical: moderateScaleVertical(10),
      borderRadius: 12,
      flex: 1,
      marginHorizontal: 5,
    },
    cardTextContainer: {
      marginTop: moderateScaleVertical(15),
    },
    cardTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    cardSubtitle: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
    teamContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      marginVertical: moderateScaleVertical(20),
      borderRadius: 12,
    },
    teamTextContainer: {
      paddingHorizontal: moderateScale(20),
      paddingBottom: moderateScaleVertical(20),
      alignItems: "center",
    },
    teamTitle: {
      fontSize: 20,
      fontWeight: "500",
      textAlign: "center",
      color: colors.text,
    },
    teamSubtitle: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
      marginTop: moderateScaleVertical(2),
      textAlign: "center",
    },
    teamButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    noConnectionContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: verticalScale(50),
    },
    noConnectionText: {
      fontSize: 16,
      fontWeight: "500",
      color: colors.text,
      textAlign: "center",
      marginBottom: verticalScale(10),
    },
    noConnectionSubText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
      textAlign: "center",
      marginBottom: verticalScale(20),
    },
    tryAgainButton: {
      flexDirection: "row",
      alignItems: "center",
      gap: verticalScale(5),
      paddingVertical: verticalScale(8),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingHorizontal: moderateScale(10),
      borderRadius: 5,
      elevation: 2,
    },
    tryAgainText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
  });
};
