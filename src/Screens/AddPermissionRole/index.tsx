import React, { useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ThemedBackButton from "../../Components/ThemedBackButton";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const AddPermissionRole = ({ navigation }: any) => {
  const [RoleName, setRoleName] = useState("");
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerLeftContainer}>
          <ThemedBackButton
            onPress={() => navigation.goBack()}
            style={{
              paddingVertical: verticalScale(5),
              paddingLeft: moderateScale(5),
              paddingRight: moderateScale(10),
            }}
            width={13}
            height={13}
          />
          <Text style={themedCommonStyles.font24W500}>Permissions</Text>
        </View>
        {/* <TouchableOpacity
          style={styles.headerRightContainer}
          activeOpacity={0.8}
        >
          <CustomIcon Icon={ICONS.PlusIcon} height={12} width={12} />
          <Text style={styles.addText}>Add</Text>
        </TouchableOpacity> */}
      </View>

      <View style={{ flex: 1 }}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Role name <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="e.g Guest accounts"
            placeholderTextColor={colors.greyText}
            value={RoleName}
            onChangeText={setRoleName}
            keyboardType="default"
          />
        </View>
      </View>

      <TouchableOpacity style={[styles.nextbtn, {}]}>
        <Text style={styles.nextBtnText}>Add role</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default AddPermissionRole;
