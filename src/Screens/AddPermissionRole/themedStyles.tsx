import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    nextbtn: {
      backgroundColor: isDarkMode ? colors.primaryBase : colors.maintext,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(20),
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      alignSelf: "center",
      justifyContent: "center",
      borderRadius: 30,
    },
    nextBtnText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 5,
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: "700",
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: "red",
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      marginHorizontal: moderateScale(10),
      paddingVertical: verticalScale(10),
      color: colors.text,
    },
  });
};
