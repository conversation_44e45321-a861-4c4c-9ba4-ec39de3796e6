import React, { useEffect, useState } from "react";
import {
  FlatList,
  Image,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { deleteClients, setClients } from "../../Redux/slices/clientSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const ManageClients = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { clients } = useAppSelector((state) => state.clients);
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const [selectAll, setSelectAll] = useState(false);
  const [search, setSearch] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [sortDirection, setSortDirection] = useState("asc"); // "asc" for A-Z, "desc" for Z-A

  // Sync local state with Redux store on mount
  useEffect(() => {
    dispatch(
      setClients(clients.map((client) => ({ ...client, checked: false })))
    );
  }, []);

  const toggleCheckbox = (id: string) => {
    const updatedClients = clients.map((client) =>
      client.id === id ? { ...client, checked: !client.checked } : client
    );
    dispatch(setClients(updatedClients));

    const allChecked = updatedClients.every((client) => client.checked);
    setSelectAll(allChecked);
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    const updatedClients = clients.map((client) => ({
      ...client,
      checked: newSelectAll,
    }));
    dispatch(setClients(updatedClients));
  };

  // Toggle sort direction
  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  // Filter and sort clients
  const filteredClients = clients
    .filter((client) =>
      `${client.name} ${client.email}`
        .toLowerCase()
        .includes(search.toLowerCase())
    )
    .sort((a, b) => {
      if (sortDirection === "asc") {
        return a.name.localeCompare(b.name);
      } else {
        return b.name.localeCompare(a.name);
      }
    });

  const handleDelete = () => {
    const clientsToDelete = clients
      .filter((client) => client.checked)
      .map((client) => client.id);
    dispatch(deleteClients(clientsToDelete));
    setSelectAll(false);
    setIsModalVisible(false);
  };

  const handleEditClient = (client: any) => {
    // Navigate to the AddNewClient screen with the client data for editing
    navigation.navigate("addClient", {
      isEditing: true,
      clientData: client,
    });
  };

  const renderItem = ({ item }: any) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => toggleCheckbox(item.id)}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.checkbox,
          item.checked && { backgroundColor: colors.primaryBase },
        ]}
      >
        {item.checked && (
          <View style={styles.checkboxInner}>
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          </View>
        )}
      </View>
      <Image source={{ uri: item.image }} style={styles.profileImage} />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{item.name}</Text>
        <Text style={styles.memberNumber}>{item.number}</Text>
      </View>
      <TouchableOpacity
        onPress={() => handleEditClient(item)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <CustomIcon Icon={ICONS.ArrowRightIcon} height={8} width={8} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <UnifiedHeader
        title="Clients"
        onBackPress={() => navigation.goBack()}
        rightButtonText="Add"
        onRightButtonPress={() => {
          navigation.navigate("addClient");
        }}
      />

      <View style={{ paddingHorizontal: verticalScale(10), flex: 1 }}>
        {clients.length > 0 ? (
          <View style={{ flex: 1 }}>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.SearchIcon} width={15} height={14.17} />
              <TextInput
                style={styles.input}
                placeholder="Search by name, email or phone number"
                placeholderTextColor={colors.greyText}
                value={search}
                onChangeText={(text) =>
                  setSearch(text.trim().length === 0 ? text.trim() : text)
                }
                keyboardType="email-address"
              />
              <TouchableOpacity
                style={styles.sortButton}
                onPress={toggleSortDirection}
                activeOpacity={0.8}
              >
                <Text style={styles.sortText}>
                  {sortDirection === "asc" ? "A-Z" : "Z-A"}
                </Text>
                <CustomIcon
                  Icon={
                    sortDirection === "asc"
                      ? isDarkMode
                        ? ICONS.WhiteArrowUpIcon
                        : ICONS.ArrowUpIcon
                      : isDarkMode
                      ? ICONS.WhiteArrowDownIcon
                      : ICONS.ArrowDownIcon
                  }
                  width={16}
                  height={16}
                />
              </TouchableOpacity>
            </View>

            {filteredClients.length > 0 && (
              <View style={styles.selectAllContainer}>
                <TouchableOpacity
                  style={styles.selectAllContainer}
                  onPress={toggleSelectAll}
                >
                  <View
                    style={[
                      styles.checkbox,
                      selectAll && { backgroundColor: colors.primaryBase },
                    ]}
                  >
                    {selectAll && (
                      <View style={styles.checkboxInner}>
                        <CustomIcon
                          Icon={ICONS.Subtract}
                          height={8}
                          width={8}
                        />
                      </View>
                    )}
                  </View>
                  <Text
                    style={{
                      fontSize: 12,
                      fontWeight: "400",
                      color: colors.greyText,
                    }}
                  >
                    {clients.filter((client) => client.checked).length > 0
                      ? `${
                          clients.filter((client) => client.checked).length
                        } CLIENTS SELECTED`
                      : "ALL CLIENTS"}
                  </Text>
                </TouchableOpacity>
                {clients.some((client) => client.checked) && (
                  <TouchableOpacity
                    style={styles.deletebtn}
                    onPress={() => setIsModalVisible(true)}
                  >
                    <CustomIcon
                      Icon={ICONS.DeleteIcon}
                      height={20}
                      width={20}
                    />
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "400",
                        color: colors.text,
                      }}
                    >
                      Delete
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            <FlatList
              data={filteredClients}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              renderItem={renderItem}
              ListEmptyComponent={() => (
                <Text style={styles.emptyText}>No Data Found.</Text>
              )}
            />
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <CustomIcon Icon={ICONS.NoClientIcon} width={108} height={108} />
            <Text style={styles.noServiceText}>No clients</Text>
            <Text style={styles.noServiceSubText}>
              Every great business starts somewhere - yours starts by adding
              your first client! 🎉
            </Text>
            <TouchableOpacity
              style={styles.addServiceButton}
              activeOpacity={0.8}
              onPress={() => {
                navigation.navigate("addClient");
              }}
            >
              <CustomIcon Icon={ICONS.SimplePlusIcon} height={20} width={20} />
              <Text style={styles.addServiceText}>Add your first client</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      <Modal transparent={true} visible={isModalVisible} animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View
              style={{
                alignItems: "center",
                paddingVertical: verticalScale(10),
              }}
            >
              <CustomIcon
                Icon={ICONS.DeleteConfirmIcon}
                height={96}
                width={96}
              />
            </View>
            <Text style={styles.modalTitle}>Delete clients</Text>
            <Text style={styles.modalSubtitle}>
              This operation cannot be undone
            </Text>
            <TouchableOpacity
              style={styles.confirmDeleteBtn}
              onPress={handleDelete}
            >
              <CustomIcon Icon={ICONS.WhiteDeleteIcon} width={20} height={20} />
              <Text style={styles.confirmDeleteText}>Yes, delete</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelDeleteBtn}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.cancelDeleteText}>No, cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ManageClients;
