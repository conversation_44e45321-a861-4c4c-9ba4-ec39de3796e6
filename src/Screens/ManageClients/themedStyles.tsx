import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    emptyContainer: {
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
    },
    noServiceText: {
      fontSize: 16,
      fontWeight: "500",
      marginTop: verticalScale(10),
      color: colors.text,
    },
    noServiceSubText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
      textAlign: "center",
    },
    addServiceButton: {
      borderWidth: 1,
      borderRadius: 10,
      borderColor: colors.border,
      padding: moderateScale(10),
      flexDirection: "row",
      gap: moderateScale(5),
      marginTop: verticalScale(20),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    addServiceText: {
      fontSize: 14,
      color: colors.text,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      paddingVertical: Platform.OS === "ios" ? 10 : 0,
      paddingHorizontal: moderateScale(10),
      marginVertical: verticalScale(10),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    sortButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: moderateScale(8),
      paddingVertical: verticalScale(20),
      borderLeftWidth: 1,
      borderLeftColor: colors.border,
    },
    sortText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
      marginRight: moderateScale(5),
    },
    selectAllContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      paddingHorizontal: 5,
      justifyContent: "space-between",
      marginVertical: 10,
    },
    memberItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 10,
      marginBottom: 10,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: moderateScale(12),
      borderRadius: 10,
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 4,
      marginRight: 8,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: isDarkMode ? "transparent" : colors.white,
    },
    checkboxInner: {},
    profileImage: {
      width: 40,
      height: 40,
      marginRight: 10,
      borderRadius: 100,
    },
    memberInfo: {
      flex: 1,
    },
    memberName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    memberNumber: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
      marginTop: verticalScale(5),
    },
    deletebtn: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(2),
      marginRight: 10,
    },
    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
      width: "90%",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    modalTitle: {
      fontSize: 24,
      fontWeight: "500",
      color: colors.text,
      textAlign: "center",
    },
    modalSubtitle: {
      fontSize: 16,
      fontWeight: "400",
      color: colors.text,
      textAlign: "center",
      marginVertical: 2,
    },
    confirmDeleteBtn: {
      backgroundColor: colors.stateerrorbase,
      padding: 10,
      borderRadius: 8,
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginVertical: verticalScale(20),
    },
    confirmDeleteText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    cancelDeleteBtn: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: 10,
    },
    cancelDeleteText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    emptyText: {
      textAlign: "center",
      color: colors.text,
    },
  });
};
