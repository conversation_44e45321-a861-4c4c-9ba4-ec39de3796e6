import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: verticalScale(20),
    },
    detailiconContainer: {
      width: 90,
      height: 90,
      borderWidth: 2,
      borderColor: isDarkMode ? colors.cardBackground : colors.white,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 66,
      marginBottom: 20,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: Platform.OS === "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    phoneCountry: {
      borderWidth: 1,
      borderColor: colors.border,
      borderTopRightRadius: 12,
      borderBottomRightRadius: 12,
      width: "76.5%",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    loginButton: {
      marginTop: moderateScaleVertical(20),
      borderRadius: 12,
      paddingVertical: moderateScaleVertical(12),
      alignItems: "center",
      justifyContent: "center",
    },
    loginText: {
      fontSize: 14,
      fontWeight: "400",
    },
    resizetext: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      justifyContent: "flex-end",
    },
    phoneview: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderTopLeftRadius: 12,
      borderBottomLeftRadius: 12,
      borderColor: colors.border,
      borderLeftWidth: 1,
      borderTopWidth: 1,
      borderBottomWidth: 1,
    },
    dategender: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(10),
      marginTop: 5,
    },
    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.5)",
    },
    modalContent: {
      width: 300,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: 20,
      borderRadius: 10,
      alignItems: "center",
    },
    modalTitle: {
      fontSize: 16,
      fontWeight: "500",
      color: colors.text,
      marginBottom: 20,
    },
    option: {
      paddingVertical: 10,
      width: "100%",
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    optionText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    cancelButton: {
      marginTop: 10,
    },
    cancelText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.stateerrorbase,
    },
    labelText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    requiredText: {
      color: "red",
    },
    dateText: {
      fontSize: 14,
      fontWeight: "400",
    },
  });
};
