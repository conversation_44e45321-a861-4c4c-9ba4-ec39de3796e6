import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import { SetupBusinessProps } from "../../Navigation/Typings";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const SetupCreateJoin = ({ navigation }: SetupBusinessProps) => {
  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView
        style={{
          flex: 1,
          paddingVertical: verticalScale(20),
        }}
      >
        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.centeredView}>
            <CustomIcon
              Icon={ICONS.LoginAppIcon}
              height={96}
              width={96}
            />
            <Text
              style={[
                themedCommonStyles.font24W500,
                { marginTop: verticalScale(10) }
              ]}
            >
              Setup your business
            </Text>
            <Text
              style={[
                themedCommonStyles.font16400,
                { marginTop: verticalScale(2) }
              ]}
            >
              Create new or join existing
            </Text>
          </View>

          <TouchableOpacity
            style={styles.newaccbutton}
            onPress={() =>
              navigation.navigate('setupSteps', { isJoinExisting: false })
            }
          >
            <CustomIcon
              Icon={ICONS.NewAccountSetupIcon}
              height={40}
              width={40}
            />
            <View style={{ flex: 1 }}>
              <Text style={styles.buttonText}>
                Create a new GlamUP account for your business, and start
                managing your services today.
              </Text>
            </View>
            <CustomIcon
              Icon={ICONS.ArrowRightIcon}
              width={8}
              height={8}
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('setupSteps', { isJoinExisting: true })
            }
            style={styles.joinexistingbtn}
          >
            <CustomIcon
              Icon={ICONS.JoinExistingIcon}
              height={40}
              width={40}
            />
            <View
              style={{
                flex: 1,
              }}
            >
              <Text style={styles.buttonText}>
                Find and connect to an existing business on GlamUP to manage
                your profile.
              </Text>
            </View>
            <CustomIcon
              Icon={ICONS.ArrowRightIcon}
              width={8}
              height={8}
            />
          </TouchableOpacity>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    </View>
  );
};

export default SetupCreateJoin;
