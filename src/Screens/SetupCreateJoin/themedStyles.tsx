import { StyleSheet } from 'react-native';
import { moderateScale, moderateScaleVertical } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeAreaContainer: {
      flex: 1,
    },
    centeredView: {
      alignItems: 'center',
    },
    newaccbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
      justifyContent: 'space-between',
      marginTop: moderateScaleVertical(25),
      borderWidth: 1,
      borderColor: colors.border,
      width: '90%',
      alignSelf: 'center',
      paddingHorizontal: moderateScale(10),
      paddingVertical: moderateScaleVertical(10),
      borderRadius: 12,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    joinexistingbtn: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: moderateScale(10),
      justifyContent: 'space-between',
      marginTop: moderateScaleVertical(15),
      borderWidth: 1,
      borderColor: colors.border,
      width: '90%',
      alignSelf: 'center',
      paddingHorizontal: moderateScale(10),
      paddingVertical: moderateScaleVertical(10),
      borderRadius: 12,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    titleText: {
      color: colors.text,
      fontSize: 24,
      fontWeight: '500',
      textAlign: 'center',
    },
    subtitleText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: '400',
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      flex: 1,
    },
  });
};
