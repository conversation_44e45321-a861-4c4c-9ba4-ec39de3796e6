import { Platform, StyleSheet } from "react-native";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: 10,
      paddingVertical: 20,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    loadingText: {
      fontSize: 14,
      fontWeight: "400",
      marginTop: 10,
      color: colors.text,
    },
    scrollContent: {
      padding: 20,
    },
    imageContainer: {
      alignItems: "center",
      marginBottom: 30,
    },
    imageWrapper: {
      position: "relative",
      marginBottom: 10,
    },
    profileImage: {
      width: 120,
      height: 120,
      borderRadius: 60,
    },
    placeholderImage: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: colors.primaryBase,
      justifyContent: "center",
      alignItems: "center",
    },
    placeholderText: {
      fontSize: 24,
      fontWeight: "500",
      color: colors.white,
    },
    editIconContainer: {
      position: "absolute",
      bottom: 0,
      right: 0,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 15,
      padding: 5,
      borderWidth: 1,
      borderColor: colors.border,
    },
    changePhotoText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    formContainer: {
      width: "100%",
    },
    inputContainer: {
      marginBottom: 20,
    },
    label: {
      fontSize: 14,
      fontWeight: "400",
      marginBottom: 5,
      color: colors.text,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: Platform.OS === "ios" ? 12 : 8,
      width: "100%",
      color: colors.text,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    disabledInput: {
      backgroundColor: isDarkMode ? colors.bglight : colors.bglight,
      color: colors.greyText,
    },
    helperText: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
      marginTop: 5,
    },
    buttonContainer: {
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    saveButton: {
      backgroundColor: colors.primaryBase,
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      width: "100%",
      alignItems: "center",
    },
    saveButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    phoneContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderTopLeftRadius: 8,
      borderBottomLeftRadius: 8,
      borderColor: colors.border,
      borderLeftWidth: 1,
      borderTopWidth: 1,
      borderBottomWidth: 1,
    },
    phoneInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: colors.border,
      borderTopRightRadius: 8,
      borderBottomRightRadius: 8,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
  });
};
