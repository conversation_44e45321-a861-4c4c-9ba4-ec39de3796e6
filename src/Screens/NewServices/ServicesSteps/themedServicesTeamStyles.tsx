import { StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedServicesTeamStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: verticalScale(10),
      color: colors.text,
    },
    subHeader: {
      fontSize: 14,
      fontWeight: '400',
      marginBottom: verticalScale(20),
      color: colors.greyText,
    },
    memberItem: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      padding: moderateScale(15),
      marginBottom: verticalScale(10),
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: colors.border,
      marginRight: moderateScale(10),
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: isDarkMode ? 'transparent' : colors.white,
    },
    memberInfo: {
      flex: 1,
    },
    memberName: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    memberEmail: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.greyText,
    },
    emptyText: {
      fontSize: 14,
      fontWeight: '400',
      textAlign: "center",
      marginTop: verticalScale(20),
      color: colors.text,
    },
  });
};
