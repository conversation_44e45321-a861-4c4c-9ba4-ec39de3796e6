import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedDurationPriceStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: verticalScale(10),
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: "red",
    },
    touchable: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: moderateScale(10),
      paddingVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(5),
    },
    textInput: {
      flex: 1,
      color: colors.text,
      fontSize: 16,
    },
    currencyContainer: {
      borderLeftWidth: 1,
      borderColor: colors.border,
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      paddingLeft: moderateScale(10),
      paddingVertical: verticalScale(5),
    },
    currencyText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.text,
      textAlign: 'center',
    },
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    dropdownContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      width: "80%",
      borderRadius: 10,
      padding: 10,
    },
    dropdownItem: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderBottomColor: colors.border,
    },
    dropdownText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    customDurationInput: {
      paddingHorizontal: 10,
      color: colors.text,
    },
    placeholderText: {
      color: colors.greyText,
    },
  });
};
