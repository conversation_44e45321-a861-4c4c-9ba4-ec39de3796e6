import React, { useState } from "react";
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Modal,
  FlatList,
} from "react-native";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import { getCurrencySymbol } from '../../../Utilities/currencyUtils';
import { useThemedDurationPriceStyles } from "./themedDurationPriceStyles";

// Define options as a constant
const options = {
  duration: ['15 min', '30 min', '45 min', '1 hour', 'Custom'],
  priceType: ['Fixed price', 'Hourly rate'],
  currency: ['EUR', 'USD', 'GBP'],
};

// Define types for Dropdown props
interface DropdownProps {
  visible: boolean;
  options: string[];
  onSelect: (value: string) => void;
  onClose: () => void;
}

const Dropdown = ({ visible, options, onSelect, onClose }: DropdownProps) => {
  const styles = useThemedDurationPriceStyles();
  
  return (
    <Modal
      transparent
      visible={visible}
      animationType='fade'
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.dropdownContainer}>
          <FlatList
            data={options}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => onSelect(item)}
              >
                <Text style={styles.dropdownText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const DurationPrice = ({
  duration,
  setDuration,
  customMinutes,
  setCustomMinutes,
  isCustomDuration,
  setIsCustomDuration,
  priceType,
  setPriceType,
  currency,
  setCurrency,
  price,
  setPrice,
}: any) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<string[]>([]);
  const [dropdownSetter, setDropdownSetter] = useState<
    ((value: string) => void) | null
  >(null);
  
  const { colors } = useTheme();
  const styles = useThemedDurationPriceStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const openDropdown = (data: string[], setter: (value: string) => void) => {
    setDropdownOptions(data);
    setDropdownSetter(() => setter);
    setDropdownVisible(true);
  };

  const handleSelect = (value: string) => {
    if (dropdownSetter) {
      if (value === 'Custom' && dropdownSetter === setDuration) {
        setIsCustomDuration(true);
        setDuration('Custom');
        setCustomMinutes('');
      } else {
        dropdownSetter(value);
        if (dropdownSetter === setDuration) {
          setIsCustomDuration(false);
        }
      }
    }
    setDropdownVisible(false);
    setDropdownSetter(null);
  };

  return (
    <View style={{ flex: 1 }}>
      <Dropdown
        visible={dropdownVisible}
        options={dropdownOptions}
        onSelect={handleSelect}
        onClose={() => setDropdownVisible(false)}
      />

      {/* Duration */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Duration <Text style={styles.required}>*</Text>
        </Text>
        <TouchableOpacity
          style={styles.touchable}
          activeOpacity={0.8}
          onPress={() => openDropdown(options.duration, setDuration)}
        >
          <Text style={styles.textInput}>{duration}</Text>
          <CustomIcon
            Icon={ICONS.DropdownIcon}
            height={12}
            width={12}
          />
        </TouchableOpacity>
      </View>

      {isCustomDuration && (
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Custom Duration <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.customDurationInput}
            placeholder='Enter minutes'
            placeholderTextColor={colors.greyText}
            keyboardType='numeric'
            value={customMinutes}
            onChangeText={setCustomMinutes}
          />
        </View>
      )}

      {/* Price Type */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Price type <Text style={styles.required}>*</Text>
        </Text>
        <TouchableOpacity
          style={styles.touchable}
          activeOpacity={0.8}
          onPress={() => openDropdown(options.priceType, setPriceType)}
        >
          <Text style={styles.textInput}>{priceType}</Text>
          <CustomIcon
            Icon={ICONS.DropdownIcon}
            height={12}
            width={12}
          />
        </TouchableOpacity>
      </View>

      {/* Price */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>
          Price <Text style={styles.required}>*</Text>
        </Text>
        <View style={styles.touchable}>
          <TextInput
            style={styles.textInput}
            placeholder={`${getCurrencySymbol('EUR')} 0.00`}
            placeholderTextColor={colors.greyText}
            value={price}
            onChangeText={setPrice}
            keyboardType='numeric'
          />
          <TouchableOpacity
            style={styles.currencyContainer}
            onPress={() => openDropdown(options.currency, setCurrency)}
          >
            <CustomIcon
              Icon={ICONS.CurrencyLogo}
              height={20}
              width={20}
            />
            <Text style={styles.currencyText}>
              {getCurrencySymbol(currency)}
            </Text>
            <CustomIcon
              Icon={ICONS.DropdownIcon}
              height={12}
              width={12}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default DurationPrice;
