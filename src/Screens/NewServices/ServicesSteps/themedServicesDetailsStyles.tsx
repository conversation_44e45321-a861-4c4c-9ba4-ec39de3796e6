import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedServicesDetailsStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    inputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      padding: verticalScale(10),
      marginBottom: moderateScale(10),
      marginTop: 5,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    label: {
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 10,
      color: colors.text,
    },
    required: {
      color: "red",
    },
    input: {
      fontSize: 14,
      fontWeight: '400',
      marginHorizontal: moderateScale(10),
      marginVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(0),
      color: colors.text,
    },
    selectedText: {
      color: colors.text,
    },
    placeholderText: {
      color: colors.greyText,
    },
    textArea: {
      height: 98,
      textAlignVertical: "top",
    },
    resizetext: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      paddingVertical: verticalScale(10),
      marginRight: moderateScale(10),
      gap: moderateScale(5),
    },
    charCount: {
      fontSize: 12,
      fontWeight: '500',
      textAlign: "right",
      color: colors.text,
    },
    touchable: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical:
        Platform.OS === "ios" ? verticalScale(10) : verticalScale(5),
    },
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    dropdownContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      width: "80%",
      borderRadius: 10,
      padding: 10,
    },
    dropdownItem: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderBottomColor: colors.border,
    },
    dropdownText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
  });
};
