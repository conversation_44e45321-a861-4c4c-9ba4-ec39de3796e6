import { StyleSheet } from "react-native";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: moderateScaleVertical(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: moderateScale(10),
    },
    bottomline: {
      width: "100%",
      borderWidth: 0.5,
      borderColor: colors.primaryBase,
      marginVertical: moderateScaleVertical(10),
      alignSelf: "center",
      borderRadius: 1,
    },
    nextbtn: {
      flexDirection: "row",
      backgroundColor: isDarkMode ? colors.primaryBase : colors.maintext,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(20),
      alignItems: "center",
      gap: moderateScale(5),
      alignSelf: "center",
      justifyContent: "center",
      borderRadius: 30,
    },
    nextBtnText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    tabContainer: {
      flexDirection: "row",
      borderBottomWidth: 1,
      borderBottomColor: colors.bgsoft,
      gap: moderateScale(15),
    },
    tab: {
      alignItems: "center",
      paddingRight: moderateScale(10),
      paddingVertical: 10,
    },
    tabRow: {
      flexDirection: "row",
      gap: moderateScale(5),
    },
    tabText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    activeTabText: {
      color: colors.primaryBase,
    },
    contentContainer: {
      flex: 1,
      padding: moderateScale(10),
    },
  });
};
