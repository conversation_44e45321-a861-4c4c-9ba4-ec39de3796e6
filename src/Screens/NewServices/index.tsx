import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import {
  addService,
  Service,
  updateService,
} from "../../Redux/slices/servicesSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import DurationPrice from "./ServicesSteps/DurationPrice";
import ServicesDetails from "./ServicesSteps/ServicesDetails";
import ServicesTeam from "./ServicesSteps/ServicesTeam";
import { useThemedStyles } from "./themedStyles";

const tabs = [
  {
    id: "Details",
    label: "Details",
    selectedIcon: ICONS.SelectedServicesDetail,
    unselectedIcon: ICONS.ServicesDetail,
  },
  {
    id: "DurationPrice",
    label: "Duration & price",
    selectedIcon: ICONS.SelectedCurrency,
    unselectedIcon: ICONS.CurrencyIcon,
  },
  {
    id: "Team",
    label: "Team",
    selectedIcon: ICONS.SelectedTeam,
    unselectedIcon: ICONS.ServicesTeam,
  },
];

const NewServices = ({ navigation, route }: any) => {
  const dispatch = useAppDispatch();
  const { members } = useAppSelector((state) => state.members);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const service: Service | undefined = route.params?.service; // Service data for editing
  const isEditing = !!service;

  const [activeIndex, setActiveIndex] = useState(0);
  const [description, setDescription] = useState("");
  const [businessName, setBusinessName] = useState("");
  const [category, setCategory] = useState("");
  const [duration, setDuration] = useState("15 min");
  const [customMinutes, setCustomMinutes] = useState("");
  const [isCustomDuration, setIsCustomDuration] = useState(false);
  const [priceType, setPriceType] = useState("Fixed price");
  const [currency, setCurrency] = useState("EUR");
  const [price, setPrice] = useState(0);
  const [teamMembers, setTeamMembers] = useState([]);

  // Prefill form when editing
  useEffect(() => {
    if (service) {
      setDescription(service.description || "");
      setBusinessName(service.businessName || "");
      setCategory(service.category || "");
      setDuration(service.duration || "15 min");
      setCustomMinutes(service.customMinutes || "");
      setIsCustomDuration(service.isCustomDuration || false);
      setPriceType(service.priceType || "Fixed price");
      setCurrency(service.currency || "EUR");
      setPrice(service.price || 0);
      setTeamMembers(service.teamMembers);
    }
  }, [service]);

  const isNextButtonDisabled = useCallback(() => {
    switch (activeIndex) {
      case 0:
        return !businessName.trim() || !category.trim();
      case 1:
        return price === 0;
      case 2:
        return false; // Team selection is optional
      default:
        return false;
    }
  }, [activeIndex, businessName, category, price]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleSaveService = () => {
    const serviceData = {
      id: service?.id || `${Date.now()}`,
      businessName,
      category,
      description,
      duration,
      customMinutes,
      isCustomDuration,
      priceType,
      currency,
      price,
      teamMembers: teamMembers.filter((member: any) => member.checked),
    };

    if (isEditing) {
      dispatch(updateService(serviceData));
    } else {
      dispatch(
        addService({
          businessName,
          category,
          description,
          duration,
          customMinutes,
          isCustomDuration,
          priceType,
          currency,
          price,
          teamMembers,
        })
      );
    }
    navigation.goBack();
  };

  const renderServicesTabs = () => {
    switch (activeIndex) {
      case 0:
        return (
          <ServicesDetails
            key="Details"
            description={description}
            setDescription={setDescription}
            businessName={businessName}
            setBusinessName={setBusinessName}
            category={category}
            setCategory={setCategory}
          />
        );
      case 1:
        return (
          <DurationPrice
            key="DurationPrice"
            duration={duration}
            setDuration={setDuration}
            customMinutes={customMinutes}
            setCustomMinutes={setCustomMinutes}
            isCustomDuration={isCustomDuration}
            setIsCustomDuration={setIsCustomDuration}
            priceType={priceType}
            setPriceType={setPriceType}
            currency={currency}
            setCurrency={setCurrency}
            price={price}
            setPrice={setPrice}
          />
        );
      case 2:
        return (
          <ServicesTeam
            key="Team"
            teamMembers={teamMembers}
            setTeamMembers={setTeamMembers}
          />
        );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <UnifiedHeader
        title={isEditing ? "Edit Service" : "New Service"}
        onBackPress={handleGoBack}
      />

      {/* Scrollable Horizontal Tab Navigation */}
      <View
        style={{
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(15),
        }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            removeClippedSubviews={false}
            style={{
              flexDirection: "row",
              borderBottomWidth: 1,
              borderBottomColor: colors.bgsoft,
              gap: moderateScale(15),
            }}
          >
            {tabs.map((item, index) => {
              const isSelected = activeIndex === index;
              return (
                <TouchableOpacity
                  key={item.id}
                  activeOpacity={1}
                  disabled
                  onPress={() => setActiveIndex(index)}
                  style={{
                    alignItems: "center",
                    paddingRight: moderateScale(10),
                    paddingVertical: 10,
                    borderColor: isSelected
                      ? colors.primaryBase
                      : colors.bgsoft,
                    borderBottomWidth: isSelected ? 1.4 : 0.2,
                  }}
                >
                  <View style={{ flexDirection: "row", gap: moderateScale(5) }}>
                    <CustomIcon
                      Icon={
                        isSelected ? item.selectedIcon : item.unselectedIcon
                      }
                      height={20}
                      width={20}
                    />
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "400",
                        color: isSelected
                          ? colors.primaryBase
                          : colors.greyText,
                      }}
                    >
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>

      {/* Render Tab Content */}
      <View style={styles.contentContainer}>{renderServicesTabs()}</View>

      {/* Next / Save Service Button */}
      <TouchableOpacity
        style={[
          styles.nextbtn,
          {
            opacity: isNextButtonDisabled() ? 0.6 : 1,
          },
        ]}
        disabled={isNextButtonDisabled()}
        onPress={() => {
          if (activeIndex < tabs.length - 1) {
            setActiveIndex(activeIndex + 1);
          } else {
            handleSaveService();
          }
        }}
      >
        <Text style={styles.nextBtnText}>
          {activeIndex === tabs.length - 1
            ? isEditing
              ? "Save Service"
              : "Add Service"
            : "Next"}
        </Text>
        {activeIndex !== tabs.length - 1 && (
          <CustomIcon Icon={ICONS.WhiteArrow} height={12} width={12} />
        )}
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default NewServices;
