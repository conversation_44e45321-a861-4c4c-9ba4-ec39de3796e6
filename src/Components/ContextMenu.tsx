import React, { FC } from "react";
import { 
  Modal, 
  StyleSheet, 
  TouchableOpacity, 
  View, 
  TouchableWithoutFeedback, 
  Text
} from "react-native";
import commonStyles from "../Utilities/Styles/commonStyles";
import { moderateScale, verticalScale } from "../Utilities/Styles/responsiveSize";
import { Colors } from "../Utilities/Styles/colors";

interface MenuItem {
  label: string;
  onPress: () => void;
  textColor?: string;
}

interface ContextMenuProps {
  isVisible: boolean;
  onClose: () => void;
  menuItems: MenuItem[];
  position: {
    top: number;
    right: number;
  };
}

const ContextMenu: FC<ContextMenuProps> = ({
  isVisible,
  onClose,
  menuItems,
  position,
}) => {
  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <View
            style={[
              styles.menuContainer,
              {
                top: position.top,
                right: position.right,
              },
            ]}
          >
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.menuItem,
                  index === menuItems.length - 1 && styles.lastMenuItem,
                ]}
                onPress={() => {
                  item.onPress();
                  onClose();
                }}
              >
                <Text
                style={{
                  ...commonStyles.font14Bold
                }}
                >
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
  },
  menuContainer: {
    position: "absolute",
    backgroundColor: Colors.bglight,
    borderRadius: 8,
    overflow: "hidden",
    width: moderateScale(150),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    paddingVertical: verticalScale(12),
    paddingHorizontal: moderateScale(16),
    borderBottomWidth: 0.6,
    borderBottomColor: Colors.white,
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
});

export default ContextMenu;
