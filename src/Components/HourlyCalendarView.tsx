import React, { FC, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../Utilities/ThemeContext';
import moment from 'moment';
import { moderateScale, verticalScale } from '../Utilities/Styles/responsiveSize';
import EditAppointmentModal from '../Utilities/Components/Modal/EditAppointmentModal';

interface HourlyCalendarViewProps {
  selectedDate: string;
  appointments: any[];
  onAddAppointment: (time: string) => void;
  onUpdateAppointment?: (appointment: any) => void;
  onDeleteAppointment?: (id: string) => void;
}

const HOUR_HEIGHT = 80; // Height for each hour slot

const HourlyCalendarView: FC<HourlyCalendarViewProps> = ({
  selectedDate,
  appointments,
  onAddAppointment,
  onUpdateAppointment,
  onDeleteAppointment,
}) => {
  const { colors, isDarkMode } = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);

  // State for edit modal
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);

  // Generate all 24 hours of the day with formatted display
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, "0");
    return {
      value: `${hour}:00`,
      display: `${hour}:00`,
      hour: i,
    };
  });

  // Filter appointments for the selected date
  const filteredAppointments = appointments.filter(
    (apt) => apt.date === selectedDate
  );

  // Calculate the duration of an appointment in minutes
  const calculateDurationInMinutes = (startTime: string, endTime: string) => {
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const [endHour, endMinute] = endTime.split(":").map(Number);

    // Calculate total minutes
    const startTotalMinutes = startHour * 60 + startMinute;
    const endTotalMinutes = endHour * 60 + endMinute;

    // Handle appointments that span to the next day
    return endTotalMinutes >= startTotalMinutes
      ? endTotalMinutes - startTotalMinutes
      : 24 * 60 - startTotalMinutes + endTotalMinutes;
  };

  // Calculate the height of an appointment based on its duration
  const calculateAppointmentHeight = (startTime: string, endTime: string) => {
    const durationMinutes = calculateDurationInMinutes(startTime, endTime);
    return (durationMinutes / 60) * HOUR_HEIGHT;
  };

  // Get appointments that start in a specific hour
  const getAppointmentsStartingInHour = (hourObj: {
    value: string;
    display: string;
    hour: number;
  }) => {
    return filteredAppointments.filter((apt) => {
      const aptHour = parseInt(apt.startTime.split(":")[0]);
      return aptHour === hourObj.hour;
    });
  };

  // Check if an appointment spans across the current hour
  const getAppointmentsSpanningHour = (hourObj: {
    value: string;
    display: string;
    hour: number;
  }) => {
    return filteredAppointments.filter((apt) => {
      const startHour = parseInt(apt.startTime.split(":")[0]);
      const endHour = parseInt(apt.endTime.split(":")[0]);
      const endMinute = parseInt(apt.endTime.split(":")[1]);

      // Check if appointment spans across this hour
      // It spans if:
      // 1. It starts before this hour AND
      // 2. It ends in this hour or later
      return (
        startHour < hourObj.hour &&
        (endHour > hourObj.hour || (endHour === hourObj.hour && endMinute > 0))
      );
    });
  };

  // Find the latest end time of appointments in a specific hour
  const getLatestEndTimeInHour = (hourObj: {
    value: string;
    display: string;
    hour: number;
  }) => {
    // Get all appointments that affect this hour (either starting in or spanning into it)
    const appointmentsInHour = [
      ...getAppointmentsStartingInHour(hourObj),
      ...getAppointmentsSpanningHour(hourObj),
    ];

    if (appointmentsInHour.length === 0) {
      return null; // No appointments in this hour
    }

    // Find the latest end time
    let latestEndTime = "00:00";

    appointmentsInHour.forEach((apt) => {
      const endHour = parseInt(apt.endTime.split(":")[0]);
      const endMinute = parseInt(apt.endTime.split(":")[1]);
      const currentEndHour = parseInt(latestEndTime.split(":")[0]);
      const currentEndMinute = parseInt(latestEndTime.split(":")[1]);

      // Compare end times
      if (
        endHour > currentEndHour ||
        (endHour === currentEndHour && endMinute > currentEndMinute)
      ) {
        latestEndTime = apt.endTime;
      }
    });

    return latestEndTime;
  };

  // Determine the color based on appointment status and theme
  const getAppointmentColor = (status: string) => {
    if (isDarkMode) {
      // Dark mode colors - more saturated and darker
      switch (status) {
        case "CANCELED":
          return "#661A1E"; // Dark red for canceled
        case "CONFIRMED":
          return "#0A5A3C"; // Dark green for confirmed
        default:
          return "#0A3A6D"; // Dark blue as default
      }
    } else {
      // Light mode colors - lighter and less saturated
      switch (status) {
        case "CANCELED":
          return "#FFEBEC"; // Light red for canceled
        case "CONFIRMED":
          return "#50fd9d"; // Light green for confirmed
        default:
          return "#007AFF"; // Light blue as default
      }
    }
  };

  // Scroll to current hour when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      const currentHour = new Date().getHours();
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          y: Math.max(0, (currentHour - 2) * HOUR_HEIGHT),
          animated: true,
        });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      {/* Edit Appointment Modal */}
      {isEditModalVisible && selectedAppointment && (
        <EditAppointmentModal
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
          appointment={selectedAppointment}
          onUpdateAppointment={(updatedAppointment) => {
            if (onUpdateAppointment) {
              onUpdateAppointment(updatedAppointment);
              setIsEditModalVisible(false);
            }
          }}
          onDeleteAppointment={(id) => {
            if (onDeleteAppointment) {
              onDeleteAppointment(id);
              setIsEditModalVisible(false);
            }
          }}
        />
      )}

      <View
        style={[styles.dateHeader, { backgroundColor: colors.cardBackground }]}
      >
        <Text style={[styles.dateText, { color: colors.text }]}>
          {moment(selectedDate).format("dddd, MMMM D, YYYY")}
        </Text>
        <Text style={[styles.subHeaderText, { color: colors.greyText }]}>
          24-Hour View
        </Text>
      </View>

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Render the hour rows */}
        {hours.map((hourObj) => {
          // Get appointments that start in this hour
          const appointmentsStartingInHour =
            getAppointmentsStartingInHour(hourObj);
          // Get appointments that span across this hour (started in previous hours)
          const appointmentsSpanningHour = getAppointmentsSpanningHour(hourObj);

          const isCurrentHour = new Date().getHours() === hourObj.hour;

          return (
            <View
              key={hourObj.value}
              style={[
                styles.hourRow,
                {
                  borderBottomColor: colors.border,
                  backgroundColor: isCurrentHour
                    ? isDarkMode
                      ? "rgba(255, 255, 255, 0.05)"
                      : "rgba(0, 0, 0, 0.03)"
                    : "transparent",
                },
              ]}
            >
              <View style={styles.timeColumn}>
                <Text
                  style={[
                    styles.timeText,
                    {
                      color: isCurrentHour ? colors.primaryBase : colors.text,
                      fontWeight: isCurrentHour ? "700" : "500",
                    },
                  ]}
                >
                  {hourObj.display}
                </Text>
                {isCurrentHour && (
                  <View
                    style={[
                      styles.currentHourIndicator,
                      { backgroundColor: colors.primaryBase },
                    ]}
                  />
                )}
              </View>

              <View style={styles.appointmentArea}>
                {/* Render appointments that start in this hour */}
                {appointmentsStartingInHour.length > 0 ||
                appointmentsSpanningHour.length > 0 ? (
                  <View style={styles.appointmentsContainer}>
                    {/* Render appointments that start in this hour */}
                    {appointmentsStartingInHour.map((apt: any) => {
                      // Calculate the height based on duration
                      const height = calculateAppointmentHeight(
                        apt.startTime,
                        apt.endTime
                      );

                      return (
                        <TouchableOpacity
                          key={apt.id}
                          style={[
                            styles.appointmentCard,
                            {
                              backgroundColor: getAppointmentColor(apt.status),
                              height: height,
                              minHeight: 40, // Ensure a minimum height for very short appointments
                            },
                          ]}
                          onPress={() => {
                            setSelectedAppointment(apt);
                            setIsEditModalVisible(true);
                          }}
                        >
                          <Text
                            style={[
                              styles.appointmentTitle,
                              { color: isDarkMode ? "#FFFFFF" : "#000000" },
                            ]}
                          >
                            {apt.title}
                          </Text>
                          <Text
                            style={[
                              styles.appointmentTime,
                              { color: isDarkMode ? "#E0E0E0" : "#333333" },
                            ]}
                          >
                            {apt.startTime} - {apt.endTime}
                          </Text>
                          <Text
                            style={[
                              styles.appointmentService,
                              { color: isDarkMode ? "#CCCCCC" : "#555555" },
                            ]}
                          >
                            {apt.service}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}

                    {/* Render appointments that span across this hour */}
                    {appointmentsSpanningHour.map((apt: any) => {
                      // Calculate how much of the appointment is visible in this hour
                      const startHour = parseInt(apt.startTime.split(":")[0]);

                      // Calculate the total height of the appointment
                      const totalHeight = calculateAppointmentHeight(
                        apt.startTime,
                        apt.endTime
                      );

                      // Calculate how many hours have passed since the appointment started
                      const hoursPassed = hourObj.hour - startHour;

                      // Calculate the height already rendered in previous hours
                      const heightAlreadyRendered = hoursPassed * HOUR_HEIGHT;

                      // Calculate the remaining height to render in this hour
                      const remainingHeight = Math.min(
                        HOUR_HEIGHT,
                        totalHeight - heightAlreadyRendered
                      );

                      // Only render if there's still height remaining
                      if (remainingHeight <= 0) return null;

                      return (
                        <TouchableOpacity
                          key={`${apt.id}-spanning-${hourObj.hour}`}
                          style={[
                            styles.appointmentCard,
                            styles.spanningAppointment,
                            {
                              backgroundColor: getAppointmentColor(apt.status),
                              height: remainingHeight,
                              minHeight: 40, // Ensure a minimum height for very short appointments
                              opacity: 0.9, // Slightly transparent to indicate it's spanning
                            },
                          ]}
                          onPress={() => {
                            setSelectedAppointment(apt);
                            setIsEditModalVisible(true);
                          }}
                        >
                          <Text
                            style={[
                              styles.appointmentTitle,
                              { color: isDarkMode ? "#FFFFFF" : "#000000" },
                            ]}
                          >
                            {apt.title}
                          </Text>
                          <Text
                            style={[
                              styles.appointmentTime,
                              { color: isDarkMode ? "#E0E0E0" : "#333333" },
                            ]}
                          >
                            {apt.startTime} - {apt.endTime}
                          </Text>
                          <Text
                            style={[
                              styles.appointmentService,
                              { color: isDarkMode ? "#CCCCCC" : "#555555" },
                            ]}
                          >
                            {apt.service}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}

                    {/* Add a "+" button if there's space for more appointments */}
                    {appointmentsStartingInHour.length +
                      appointmentsSpanningHour.length <
                      2 && (
                      <TouchableOpacity
                        style={[
                          styles.addMoreButton,
                          { borderColor: colors.primaryBase },
                        ]}
                        onPress={() => {
                          // Find available time in this hour
                          const hourStart = hourObj.hour;

                          // Get the latest end time of any appointment in this hour
                          const latestEndTime = getLatestEndTimeInHour(hourObj);

                          if (latestEndTime) {
                            const endHour = parseInt(
                              latestEndTime.split(":")[0]
                            );

                            // If the latest end time is in this hour, use it as the start time
                            if (endHour === hourStart) {
                              onAddAppointment(latestEndTime);
                            } else {
                              // Find the next available 15-minute slot after the latest end time
                              // Combine all appointments in this hour
                              const allAppointmentsInHour = [
                                ...appointmentsStartingInHour,
                                ...appointmentsSpanningHour,
                              ];

                              const existingSlots = allAppointmentsInHour.map(
                                (apt: any) => {
                                  // For appointments starting in this hour
                                  if (
                                    parseInt(apt.startTime.split(":")[0]) ===
                                    hourStart
                                  ) {
                                    const startMinutes = parseInt(
                                      apt.startTime.split(":")[1]
                                    );
                                    const endMinutes = parseInt(
                                      apt.endTime.split(":")[1]
                                    );
                                    const endHour = parseInt(
                                      apt.endTime.split(":")[0]
                                    );

                                    return {
                                      start: startMinutes,
                                      end:
                                        endHour > hourStart ? 60 : endMinutes,
                                    };
                                  }
                                  // For appointments spanning into this hour
                                  else {
                                    const endMinutes = parseInt(
                                      apt.endTime.split(":")[1]
                                    );
                                    const endHour = parseInt(
                                      apt.endTime.split(":")[0]
                                    );

                                    return {
                                      start: 0, // Starts at the beginning of this hour
                                      end:
                                        endHour > hourStart ? 60 : endMinutes,
                                    };
                                  }
                                }
                              );

                              // Find a 15-minute slot that doesn't overlap
                              let availableSlot = null;
                              const slotDuration = 15; // 15-minute slots

                              for (
                                let minute = 0;
                                minute < 60;
                                minute += slotDuration
                              ) {
                                const slotEnd = minute + slotDuration;
                                const isOverlapping = existingSlots.some(
                                  (slot: any) =>
                                    minute < slot.end && slotEnd > slot.start
                                );

                                if (!isOverlapping) {
                                  availableSlot = `${hourStart
                                    .toString()
                                    .padStart(2, "0")}:${minute
                                    .toString()
                                    .padStart(2, "0")}`;
                                  break;
                                }
                              }

                              if (availableSlot) {
                                onAddAppointment(availableSlot);
                              } else {
                                Alert.alert(
                                  "No Available Slots",
                                  `No available time slots in this hour. All slots are booked.`
                                );
                              }
                            }
                          } else {
                            // No appointments in this hour, can book from the start
                            onAddAppointment(hourObj.value);
                          }
                        }}
                      >
                        <Text
                          style={[
                            styles.addMoreText,
                            { color: colors.primaryBase },
                          ]}
                        >
                          +
                        </Text>
                        <Text
                          style={[
                            styles.addMoreLabel,
                            { color: colors.primaryBase },
                          ]}
                        >
                          Add
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                ) : (
                  <TouchableOpacity
                    style={[styles.emptySlot, { borderColor: colors.border }]}
                    onPress={() => {
                      // Get all appointments that affect this hour
                      const appointmentsStartingInHour =
                        getAppointmentsStartingInHour(hourObj);
                      const appointmentsSpanningHour =
                        getAppointmentsSpanningHour(hourObj);
                      const allAppointmentsInHour = [
                        ...appointmentsStartingInHour,
                        ...appointmentsSpanningHour,
                      ];

                      if (allAppointmentsInHour.length > 0) {
                        // There are appointments in this hour
                        // Find the next available time slot

                        // Create time slots for existing appointments
                        const existingSlots = allAppointmentsInHour.map(
                          (apt: any) => {
                            const startHour = parseInt(
                              apt.startTime.split(":")[0]
                            );
                            const startMinute = parseInt(
                              apt.startTime.split(":")[1]
                            );
                            const endHour = parseInt(apt.endTime.split(":")[0]);
                            const endMinute = parseInt(
                              apt.endTime.split(":")[1]
                            );

                            // For appointments starting in this hour
                            if (startHour === hourObj.hour) {
                              return {
                                start: startMinute,
                                end: endHour > hourObj.hour ? 60 : endMinute,
                              };
                            }
                            // For appointments spanning into this hour
                            else {
                              return {
                                start: 0, // Starts at the beginning of this hour
                                end: endHour > hourObj.hour ? 60 : endMinute,
                              };
                            }
                          }
                        );

                        // Find a 15-minute slot that doesn't overlap
                        let availableSlot = null;
                        const slotDuration = 15; // 15-minute slots

                        for (
                          let minute = 0;
                          minute < 60;
                          minute += slotDuration
                        ) {
                          const slotEnd = minute + slotDuration;
                          const isOverlapping = existingSlots.some(
                            (slot: any) =>
                              minute < slot.end && slotEnd > slot.start
                          );

                          if (!isOverlapping) {
                            availableSlot = `${hourObj.hour
                              .toString()
                              .padStart(2, "0")}:${minute
                              .toString()
                              .padStart(2, "0")}`;
                            break;
                          }
                        }

                        if (availableSlot) {
                          onAddAppointment(availableSlot);
                        } else {
                          // No available slots in this hour
                          Alert.alert(
                            "Hour Fully Booked",
                            `No available time slots in this hour. All slots are booked.`
                          );
                        }
                      } else {
                        // No appointments in this hour, can book from the start
                        onAddAppointment(hourObj.value);
                      }
                    }}
                  />
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dateHeader: {
    padding: moderateScale(10),
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  dateText: {
    fontSize: 16,
    fontWeight: "600",
  },
  subHeaderText: {
    fontSize: 12,
    marginTop: 4,
  },
  scrollContent: {
    paddingBottom: verticalScale(20),
  },
  hourRow: {
    flexDirection: "row",
    height: HOUR_HEIGHT,
    borderBottomWidth: 1,
  },
  timeColumn: {
    width: 70,
    justifyContent: "center",
    alignItems: "center",
    paddingRight: moderateScale(10),
    position: "relative",
  },
  timeText: {
    fontSize: 15,
    fontWeight: "500",
  },
  currentHourIndicator: {
    position: "absolute",
    left: 0,
    width: 4,
    height: "70%",
    borderRadius: 2,
  },
  appointmentArea: {
    flex: 1,
    padding: moderateScale(5),
  },
  emptySlot: {
    flex: 1,
    borderWidth: 1,
    borderStyle: "dashed",
    borderRadius: 5,
    opacity: 0.3,
  },
  appointmentsContainer: {
    flex: 1,
    flexDirection: "column",
    gap: 5,
  },
  appointmentCard: {
    flex: 1,
    borderRadius: 5,
    padding: moderateScale(8),
    justifyContent: "center",
    marginBottom: 5,
    overflow: "hidden",
  },
  spanningAppointment: {
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.5)",
    borderStyle: "dashed",
  },
  appointmentTitle: {
    fontSize: 14,
    fontWeight: "600",
  },
  appointmentTime: {
    fontSize: 12,
  },
  appointmentService: {
    fontSize: 12,
    marginTop: 2,
  },
  addMoreButton: {
    height: 40,
    borderRadius: 5,
    borderWidth: 1,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
  addMoreText: {
    fontSize: 18,
    fontWeight: "700",
    marginRight: 5,
  },
  addMoreLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
});

export default HourlyCalendarView;