import React from "react";
import {
  Modal,
  TouchableOpacity,
  View,
  Text,
  FlatList,
  StyleSheet,
} from "react-native";
import moment from "moment";
import { useTheme } from "../Utilities/ThemeContext";
import CustomIcon from "./CustomIcon";
import ICONS from "../Assets/Icon";

interface MonthDropdownProps {
  isVisible: boolean;
  onClose: () => void;
  currentMonth: moment.Moment;
  onSelectMonth: (month: moment.Moment) => void;
}

const MonthDropdown: React.FC<MonthDropdownProps> = ({
  isVisible,
  onClose,
  currentMonth,
  onSelectMonth,
}) => {
  const { colors, isDarkMode } = useTheme();

  // Generate months for the current year and next year
  const generateMonths = () => {
    const months = [];
    const currentYear = moment().year();
    
    // Add months from last year (last 6 months)
    for (let i = 6; i >= 1; i--) {
      const month = moment().subtract(i, 'months').startOf('month');
      months.push(month);
    }
    
    // Add current month
    months.push(moment().startOf('month'));
    
    // Add next 12 months
    for (let i = 1; i <= 12; i++) {
      const month = moment().add(i, 'months').startOf('month');
      months.push(month);
    }
    
    return months;
  };

  const months = generateMonths();

  const renderMonthItem = ({ item }: { item: moment.Moment }) => {
    const isSelected = item.isSame(currentMonth, 'month');
    const isCurrentMonth = item.isSame(moment(), 'month');
    
    return (
      <TouchableOpacity
        style={[
          styles.monthItem,
          {
            backgroundColor: isSelected
              ? colors.primaryBase
              : isDarkMode
              ? colors.cardBackground
              : colors.white,
            borderColor: colors.border,
          },
        ]}
        onPress={() => {
          onSelectMonth(item);
          onClose();
        }}
      >
        <Text
          style={[
            styles.monthText,
            {
              color: isSelected
                ? colors.white
                : colors.text,
              fontWeight: isCurrentMonth ? 'bold' : 'normal',
            },
          ]}
        >
          {item.format("MMMM YYYY")}
        </Text>
        {isCurrentMonth && !isSelected && (
          <View style={[styles.currentIndicator, { backgroundColor: colors.primaryBase }]} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View
          style={[
            styles.dropdown,
            {
              backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
              borderColor: colors.border,
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={[styles.headerText, { color: colors.text }]}>
              Select Month
            </Text>
            <TouchableOpacity onPress={onClose}>
              <CustomIcon Icon={ICONS.CrossIcon} width={15} height={15} />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={months}
            renderItem={renderMonthItem}
            keyExtractor={(item) => item.format("YYYY-MM")}
            showsVerticalScrollIndicator={false}
            style={styles.monthList}
            initialScrollIndex={months.findIndex(month => month.isSame(currentMonth, 'month'))}
            getItemLayout={(data, index) => ({
              length: 50,
              offset: 50 * index,
              index,
            })}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  dropdown: {
    width: 280,
    maxHeight: 400,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "600",
  },
  monthList: {
    maxHeight: 300,
  },
  monthItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    height: 50,
  },
  monthText: {
    fontSize: 16,
    fontWeight: "500",
  },
  currentIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default MonthDropdown;
