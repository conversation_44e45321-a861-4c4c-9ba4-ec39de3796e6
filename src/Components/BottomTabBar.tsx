import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import React, { FC, useCallback } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../Assets/Icon";
import useNetworkStatus from "../Hooks/useNetworkStatus";
import { SetIsAddNewVisible } from "../Redux/slices/modalSlice";
import { useAppDispatch } from "../Redux/store";
import AddNewModal from "../Utilities/Components/Modal/AddNewModal";
import { useTheme } from "../Utilities/ThemeContext";
import { verticalScale } from "../Utilities/Styles/responsiveSize";
import CustomIcon from "./CustomIcon";

type Tab = {
  name: string;
  icon: any;
  activIcon: any;
  route: string;
  initialScreen: string;
};

const tabs: Tab[] = [
  {
    name: "OverView",
    icon: ICONS.UnselectedOverview,
    activIcon: ICONS.CalenderTabIcon,
    route: "overView",
    initialScreen: "Overview",
  },
  {
    name: "Manage",
    icon: ICONS.ManageTabIcon,
    activIcon: ICONS.SelectedManageIcon,
    route: "manage",
    initialScreen: "manage",
  },
];

const BottomTabBar: FC<BottomTabBarProps> = (props) => {
  const dispatch = useAppDispatch();
  const { isConnected, isInternetReachable, type } = useNetworkStatus();
  const { colors, isDarkMode } = useTheme();

  const insets = useSafeAreaInsets();
  const { state, navigation, descriptors } = props;
  const currentRoute = state.routes[state.index].name;

  const handleTabPress = useCallback(
    (tab: Tab) => {
      if (currentRoute !== tab.route) {
        navigation.navigate(tab.route);
      }
    },
    [navigation, currentRoute]
  );

  return (
    <View
      style={[
        styles.container,
        {
          paddingBottom: insets.bottom + verticalScale(10),
          backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
          borderTopWidth: 0.5,
          borderTopColor: colors.border,
        },
      ]}
    >
      <TouchableOpacity
        style={styles.tab}
        onPress={() => handleTabPress(tabs[0])}
        activeOpacity={0.7}
      >
        <CustomIcon
          Icon={
            tabs[0].route === currentRoute ? tabs[0].activIcon : tabs[0].icon
          }
          height={24}
          width={24}
        />
        <Text
          style={{
            alignSelf: "flex-end",
            fontSize: 11,
            color:
              tabs[0].route === currentRoute
                ? colors.stateerrorbase
                : colors.text,
          }}
        >
          {tabs[0].name}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => {
          if (isConnected) {
            dispatch(SetIsAddNewVisible(true));
          } else {
            Toast.show({
              type: "error",
              text1: "Please make sure you have working Internet connection.",
            });
          }
        }}
        activeOpacity={0.7}
      >
        <CustomIcon Icon={ICONS.PlusTabIcon} height={48} width={48} />
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.tab}
        onPress={() => handleTabPress(tabs[1])}
        activeOpacity={0.7}
      >
        <CustomIcon
          Icon={
            tabs[1].route === currentRoute ? tabs[1].activIcon : tabs[1].icon
          }
          height={24}
          width={24}
        />
        <Text
          style={{
            alignSelf: "flex-end",
            fontSize: 11,
            color:
              tabs[1].route === currentRoute
                ? colors.stateerrorbase
                : colors.text,
          }}
        >
          {tabs[1].name}
        </Text>
      </TouchableOpacity>
      <AddNewModal />
    </View>
  );
};

export default BottomTabBar;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    paddingTop: verticalScale(20),
  },

  tab: {
    alignItems: "center",
    justifyContent: "flex-end",
    gap: verticalScale(5),
  },
});
