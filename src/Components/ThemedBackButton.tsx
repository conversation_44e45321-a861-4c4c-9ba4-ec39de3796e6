import React from 'react';
import { TouchableOpacity, StyleProp, ViewStyle } from 'react-native';
import { useTheme } from '../Utilities/ThemeContext';
import ICONS from '../Assets/Icon';
import CustomIcon from './CustomIcon';

interface ThemedBackButtonProps {
  onPress: () => void;
  width?: number;
  height?: number;
  style?: StyleProp<ViewStyle>;
}

const ThemedBackButton: React.FC<ThemedBackButtonProps> = ({
  onPress,
  width = 16,
  height = 16,
  style,
}) => {
  const { isDarkMode } = useTheme();
  
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onPress}
      style={style}
    >
      <CustomIcon
        Icon={isDarkMode ? ICONS.WhiteBackIcon : ICONS.ArrowLeftIcon}
        width={width }
        height={height}
      />
    </TouchableOpacity>
  );
};

export default ThemedBackButton;
