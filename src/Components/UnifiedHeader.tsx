import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../Utilities/ThemeContext';
import { useThemedCommonStyles } from '../Utilities/Styles/themedCommonStyles';
import { moderateScale, verticalScale } from '../Utilities/Styles/responsiveSize';
import CustomIcon from './CustomIcon';
import ICONS from '../Assets/Icon';
import ThemedBackButton from './ThemedBackButton';

interface UnifiedHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightButtonText?: string;
  onRightButtonPress?: () => void;
  rightButtonIcon?: any;
  containerStyle?: StyleProp<ViewStyle>;
}

/**
 * A unified header component for consistent headers across all screens
 */
const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  title,
  showBackButton = true,
  onBackPress,
  rightButtonText,
  onRightButtonPress,
  rightButtonIcon = ICONS.PlusIcon,
  containerStyle,
}) => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const themedCommonStyles = useThemedCommonStyles();
  
  // Default back handler uses navigation.goBack()
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.headerContainer, containerStyle]}>
      <View style={styles.headerLeftContainer}>
        {showBackButton && (
          <ThemedBackButton
            onPress={handleBackPress}
            style={styles.backButton}
            width={13}
            height={13}
          />
        )}
        <Text style={themedCommonStyles.font24W500}>{title}</Text>
      </View>
      
      {rightButtonText && onRightButtonPress && (
        <TouchableOpacity
          style={styles.headerRightContainer}
          activeOpacity={0.8}
          onPress={onRightButtonPress}
        >
          <CustomIcon
            Icon={rightButtonIcon}
            height={12}
            width={12}
          />
          <Text style={[styles.rightButtonText, { color: colors.primaryBase }]}>
            {rightButtonText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    paddingVertical: verticalScale(5),
    paddingLeft: moderateScale(5),
    paddingRight: moderateScale(10),
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(5),
  },
  rightButtonText: {
    fontSize: 14,
    fontWeight: '400',
  },
});

export default UnifiedHeader;
