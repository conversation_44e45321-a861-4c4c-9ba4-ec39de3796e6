import React from 'react';
import { StatusBar } from 'react-native';
import { useTheme } from '../Utilities/ThemeContext';
import { Colors } from "../Utilities/Styles/colors";

interface ThemedStatusBarProps {
  // Optional props can be added here if needed
}

const ThemedStatusBar: React.FC<ThemedStatusBarProps> = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <StatusBar
      barStyle={isDarkMode ? "light-content" : "dark-content"}
      backgroundColor={isDarkMode ? "#121212" : Colors.bglight}
      translucent={false}
    />
  );
};

export default ThemedStatusBar;
