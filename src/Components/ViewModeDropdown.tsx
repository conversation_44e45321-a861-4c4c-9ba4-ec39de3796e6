import React, { FC } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import { useTheme } from '../Utilities/ThemeContext';
import CustomIcon from './CustomIcon';
import ICONS from '../Assets/Icon';
import { moderateScale, verticalScale } from '../Utilities/Styles/responsiveSize';

interface ViewModeDropdownProps {
  isVisible: boolean;
  onClose: () => void;
  currentMode: "Hourly" | "Weekly" | "Monthly";
  onSelectMode: (mode: "Hourly" | "Weekly" | "Monthly") => void;
}

const ViewModeDropdown: FC<ViewModeDropdownProps> = ({
  isVisible,
  onClose,
  currentMode,
  onSelectMode,
}) => {
  const { colors, isDarkMode } = useTheme();

  const handleSelectMode = (mode: "Hourly" | "Weekly" | "Monthly") => {
    onSelectMode(mode);
    onClose();
  };

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View
              style={[
                styles.dropdown,
                {
                  backgroundColor: isDarkMode
                    ? colors.cardBackground
                    : colors.white,
                  borderColor: colors.border,
                },
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.option,
                  currentMode === "Hourly" && {
                    backgroundColor: isDarkMode
                      ? colors.bglight
                      : colors.bgsoft + "40", // Add transparency
                  },
                ]}
                onPress={() => handleSelectMode("Hourly")}
              >
                <Text
                  style={[
                    styles.optionText,
                    { color: colors.text },
                    currentMode === "Hourly" && { fontWeight: "600" },
                  ]}
                >
                  Hourly
                </Text>
                {currentMode === "Hourly" && (
                  <CustomIcon
                    Icon={ICONS.CheckRightIcon}
                    height={16}
                    width={16}
                  />
                )}
              </TouchableOpacity>

              <View
                style={[styles.divider, { backgroundColor: colors.border }]}
              />

              <TouchableOpacity
                style={[
                  styles.option,
                  currentMode === "Weekly" && {
                    backgroundColor: isDarkMode
                      ? colors.bglight
                      : colors.bgsoft + "40", // Add transparency
                  },
                ]}
                onPress={() => handleSelectMode("Weekly")}
              >
                <Text
                  style={[
                    styles.optionText,
                    { color: colors.text },
                    currentMode === "Weekly" && { fontWeight: "600" },
                  ]}
                >
                  Weekly
                </Text>
                {currentMode === "Weekly" && (
                  <CustomIcon
                    Icon={ICONS.CheckRightIcon}
                    height={16}
                    width={16}
                  />
                )}
              </TouchableOpacity>

              <View
                style={[styles.divider, { backgroundColor: colors.border }]}
              />

              <TouchableOpacity
                style={[
                  styles.option,
                  currentMode === "Monthly" && {
                    backgroundColor: isDarkMode
                      ? colors.bglight
                      : colors.bgsoft + "40", // Add transparency
                  },
                ]}
                onPress={() => handleSelectMode("Monthly")}
              >
                <Text
                  style={[
                    styles.optionText,
                    { color: colors.text },
                    currentMode === "Monthly" && { fontWeight: "600" },
                  ]}
                >
                  Monthly
                </Text>
                {currentMode === "Monthly" && (
                  <CustomIcon
                    Icon={ICONS.CheckRightIcon}
                    height={16}
                    width={16}
                  />
                )}
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  dropdown: {
    marginTop: verticalScale(70), // Position below the header
    marginRight: moderateScale(20),
    width: moderateScale(150),
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: verticalScale(12),
    paddingHorizontal: moderateScale(16),
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    width: '100%',
  },
});

export default ViewModeDropdown;
