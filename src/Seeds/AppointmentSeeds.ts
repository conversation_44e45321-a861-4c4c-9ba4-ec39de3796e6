const getWeekDates = (offset = 0) => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 for Sunday, 1 for Monday
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)); // Adjust to Monday

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i + offset * 7);
    weekDates.push(currentDate.toISOString().split("T")[0]); // Format as YYYY-MM-DD
  }
  return weekDates;
};

// Generate dates for different months to better test monthly view
const getMonthDates = (monthOffset = 0) => {
  const today = new Date();
  const targetMonth = new Date(today.getFullYear(), today.getMonth() + monthOffset, 1);
  const daysInMonth = new Date(targetMonth.getFullYear(), targetMonth.getMonth() + 1, 0).getDate();

  const monthDates = [];
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(targetMonth.getFullYear(), targetMonth.getMonth(), day);
    monthDates.push(date.toISOString().split("T")[0]);
  }
  return monthDates;
};

const thisWeekDates = getWeekDates(0);
const nextWeekDates = getWeekDates(1);
const thisMonthDates = getMonthDates(0);
const nextMonthDates = getMonthDates(1);
const lastMonthDates = getMonthDates(-1);

export const appointments = [
  {
    id: "1",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "9:00",
    endTime: "9:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "11",
    clientId: "1",
    clientName: "Dean Ambrose",
    startTime: "11:00",
    endTime: "12:30",
    title: "Dean Ambrose",
    status: "CANCELED",
    date: thisWeekDates[1], // Tuesday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "12",
    clientId: "1",
    clientName: "Brock Lesnar",
    startTime: "11:30",
    endTime: "12:30",
    title: "Brock Lesnar",
    status: "CONFIRMED",
    date: thisWeekDates[2], // Wednesday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "201",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[3], // Thursday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "3",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "9:00",
    endTime: "9:30",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[6], // Sunday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "4",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "10:15",
    endTime: "11:00",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "5",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "10:00",
    endTime: "10:15",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "6",
    clientId: "6",
    clientName: "Enis Ho",
    startTime: "9:00",
    endTime: "10:00",
    title: "CANCELED: Enis Ho...",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },

  {
    id: "8",
    clientId: "8",
    clientName: "Matteo Rossi",
    startTime: "11:30",
    endTime: "12:45",
    title: "Matteo Rossi",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "90",
    clientId: "9",
    clientName: "David Smith",
    startTime: "12:00",
    endTime: "12:30",
    title: "Da...",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "2",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "13",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "10:00",
    endTime: "10:30",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "14",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "11:00",
    endTime: "11:45",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "15",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "12:00",
    endTime: "12:30",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "16",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "13:00",
    endTime: "13:45",
    title: "Theo Van Dijk",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "17",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "10:30",
    endTime: "11:00",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisWeekDates[1], // Tuesday this week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "18",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "11:00",
    endTime: "11:30",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "19",
    clientId: "6",
    clientName: "Enis Ho",
    startTime: "14:00",
    endTime: "14:45",
    title: "Enis Ho",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "20",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "9:00",
    endTime: "9:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisWeekDates[2], // Wednesday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "21",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "10:00",
    endTime: "10:30",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "22",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "11:30",
    endTime: "11:45",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "24",
    clientId: "9",
    clientName: "David Smith",
    startTime: "13:00",
    endTime: "13:30",
    title: "David Smith",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "25",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[6], // Sunday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "26",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "10:30",
    endTime: "11:00",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "27",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "11:45",
    endTime: "12:00",
    title: "Theo Van Dijk",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "28",
    clientId: "8",
    clientName: "Matteo Rossi",
    startTime: "12:30",
    endTime: "13:00",
    title: "Matteo Rossi",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "29",
    clientId: "9",
    clientName: "David Smith",
    startTime: "14:00",
    endTime: "14:30",
    title: "David Smith",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  // --- Adding more appointments for April ---
  {
    id: "30",
    clientId: "10",
    clientName: "Olivia Williams",
    startTime: "10:00",
    endTime: "10:45",
    title: "Olivia Williams",
    status: "CONFIRMED",
    date: nextWeekDates[0], // Monday next week
    service: "Women's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "31",
    clientId: "11",
    clientName: "James Brown",
    startTime: "14:30",
    endTime: "15:00",
    title: "James Brown",
    status: "CONFIRMED",
    date: nextWeekDates[0], // Monday next week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "32",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "15:00",
    endTime: "15:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: nextWeekDates[1], // Tuesday next week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "33",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "11:30",
    endTime: "12:00",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: nextWeekDates[1], // Tuesday next week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "34",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "9:00",
    endTime: "9:45",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: nextWeekDates[2], // Wednesday next week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "35",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "13:30",
    endTime: "14:00",
    title: "Theo Van Dijk",
    status: "CONFIRMED",
    date: nextWeekDates[2], // Wednesday next week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "36",
    clientId: "9",
    clientName: "David Smith",
    startTime: "16:00",
    endTime: "16:30",
    title: "David Smith",
    status: "CONFIRMED",
    date: nextWeekDates[3], // Thursday next week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "37",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "10:30",
    endTime: "11:15",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: nextWeekDates[3], // Thursday next week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },

  // --- Appointments spread across different months for better monthly view testing ---

  // Last Month Appointments
  {
    id: "100",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "9:00",
    endTime: "9:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: lastMonthDates[5], // 6th day of last month
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1",
  },
  {
    id: "101",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "14:00",
    endTime: "14:45",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: lastMonthDates[15], // 16th day of last month
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2",
  },
  {
    id: "102",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "11:30",
    endTime: "12:15",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: lastMonthDates[25], // 26th day of last month
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "3",
  },

  // This Month Appointments (spread throughout the month)
  {
    id: "103",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "10:00",
    endTime: "10:45",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisMonthDates[2], // 3rd day of this month
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1",
  },
  {
    id: "104",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "15:30",
    endTime: "16:00",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisMonthDates[8], // 9th day of this month
    service: "Beard Trim",
    teamMember: "Gwenna Evans",
    teamMemberId: "2",
  },
  {
    id: "105",
    clientId: "6",
    clientName: "Enis Ho",
    startTime: "13:00",
    endTime: "13:45",
    title: "Enis Ho",
    status: "CONFIRMED",
    date: thisMonthDates[12], // 13th day of this month
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3",
  },
  {
    id: "106",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "9:30",
    endTime: "10:15",
    title: "Theo Van Dijk",
    status: "CONFIRMED",
    date: thisMonthDates[18], // 19th day of this month
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1",
  },
  {
    id: "107",
    clientId: "8",
    clientName: "Matteo Rossi",
    startTime: "16:00",
    endTime: "16:30",
    title: "Matteo Rossi",
    status: "CONFIRMED",
    date: thisMonthDates[22], // 23rd day of this month
    service: "Beard Trim",
    teamMember: "Gwenna Evans",
    teamMemberId: "2",
  },

  // Next Month Appointments
  {
    id: "108",
    clientId: "9",
    clientName: "David Smith",
    startTime: "11:00",
    endTime: "11:45",
    title: "David Smith",
    status: "CONFIRMED",
    date: nextMonthDates[4], // 5th day of next month
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3",
  },
  {
    id: "109",
    clientId: "10",
    clientName: "Olivia Williams",
    startTime: "14:30",
    endTime: "15:15",
    title: "Olivia Williams",
    status: "CONFIRMED",
    date: nextMonthDates[10], // 11th day of next month
    service: "Women's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1",
  },
  {
    id: "110",
    clientId: "11",
    clientName: "James Brown",
    startTime: "12:00",
    endTime: "12:30",
    title: "James Brown",
    status: "CONFIRMED",
    date: nextMonthDates[20], // 21st day of next month
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2",
  },

  // Additional appointments for better monthly distribution
  {
    id: "111",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "10:30",
    endTime: "11:00",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisMonthDates[28], // 29th day of this month (near end)
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "3",
  },
  {
    id: "112",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "8:30",
    endTime: "9:15",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: nextMonthDates[1], // 2nd day of next month (early in month)
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1",
  },
];
