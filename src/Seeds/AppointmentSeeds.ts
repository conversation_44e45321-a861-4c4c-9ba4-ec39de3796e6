const getWeekDates = (offset = 0) => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 for Sunday, 1 for Monday
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)); // Adjust to Monday

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i + offset * 7);
    weekDates.push(currentDate.toISOString().split("T")[0]); // Format as YYYY-MM-DD
  }
  return weekDates;
};

const thisWeekDates = getWeekDates(0);
const nextWeekDates = getWeekDates(1);

export const appointments = [
  {
    id: "1",
    clientId: "1",
    clientName: "<PERSON>",
    startTime: "9:00",
    endTime: "9:30",
    title: "<PERSON>",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "<PERSON>",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "11",
    clientId: "1",
    clientName: "Dean Ambrose",
    startTime: "11:00",
    endTime: "12:30",
    title: "Dean Ambrose",
    status: "CANCELED",
    date: thisWeekDates[1], // Tuesday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "12",
    clientId: "1",
    clientName: "Brock Lesnar",
    startTime: "11:30",
    endTime: "12:30",
    title: "Brock Lesnar",
    status: "CONFIRMED",
    date: thisWeekDates[2], // Wednesday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "201",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[3], // Thursday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "3",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "9:00",
    endTime: "9:30",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[6], // Sunday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "4",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "10:15",
    endTime: "11:00",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "5",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "10:00",
    endTime: "10:15",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "6",
    clientId: "6",
    clientName: "Enis Ho",
    startTime: "9:00",
    endTime: "10:00",
    title: "CANCELED: Enis Ho...",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },

  {
    id: "8",
    clientId: "8",
    clientName: "Matteo Rossi",
    startTime: "11:30",
    endTime: "12:45",
    title: "Matteo Rossi",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "90",
    clientId: "9",
    clientName: "David Smith",
    startTime: "12:00",
    endTime: "12:30",
    title: "Da...",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "2",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "13",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "10:00",
    endTime: "10:30",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "14",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "11:00",
    endTime: "11:45",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "15",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "12:00",
    endTime: "12:30",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "16",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "13:00",
    endTime: "13:45",
    title: "Theo Van Dijk",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "17",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "10:30",
    endTime: "11:00",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisWeekDates[1], // Tuesday this week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "18",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "11:00",
    endTime: "11:30",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "19",
    clientId: "6",
    clientName: "Enis Ho",
    startTime: "14:00",
    endTime: "14:45",
    title: "Enis Ho",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "20",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "9:00",
    endTime: "9:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: thisWeekDates[2], // Wednesday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "21",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "10:00",
    endTime: "10:30",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "22",
    clientId: "4",
    clientName: "Miloš Petrović",
    startTime: "11:30",
    endTime: "11:45",
    title: "Miloš Petrović",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "24",
    clientId: "9",
    clientName: "David Smith",
    startTime: "13:00",
    endTime: "13:30",
    title: "David Smith",
    status: "CANCELED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "25",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "9:30",
    endTime: "10:00",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: thisWeekDates[6], // Sunday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "26",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "10:30",
    endTime: "11:00",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Beard Trim",
    teamMember: "Victor Harvley",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "27",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "11:45",
    endTime: "12:00",
    title: "Theo Van Dijk",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "28",
    clientId: "8",
    clientName: "Matteo Rossi",
    startTime: "12:30",
    endTime: "13:00",
    title: "Matteo Rossi",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "29",
    clientId: "9",
    clientName: "David Smith",
    startTime: "14:00",
    endTime: "14:30",
    title: "David Smith",
    status: "CONFIRMED",
    date: thisWeekDates[0], // Monday this week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  // --- Adding more appointments for April ---
  {
    id: "30",
    clientId: "10",
    clientName: "Olivia Williams",
    startTime: "10:00",
    endTime: "10:45",
    title: "Olivia Williams",
    status: "CONFIRMED",
    date: nextWeekDates[0], // Monday next week
    service: "Women's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "31",
    clientId: "11",
    clientName: "James Brown",
    startTime: "14:30",
    endTime: "15:00",
    title: "James Brown",
    status: "CONFIRMED",
    date: nextWeekDates[0], // Monday next week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "32",
    clientId: "1",
    clientName: "Sebastian Novak",
    startTime: "15:00",
    endTime: "15:30",
    title: "Sebastian Novak",
    status: "CONFIRMED",
    date: nextWeekDates[1], // Tuesday next week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "33",
    clientId: "3",
    clientName: "Elias Nilsen",
    startTime: "11:30",
    endTime: "12:00",
    title: "Elias Nilsen",
    status: "CONFIRMED",
    date: nextWeekDates[1], // Tuesday next week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "34",
    clientId: "5",
    clientName: "Viktor Horváth",
    startTime: "9:00",
    endTime: "9:45",
    title: "Viktor Horváth",
    status: "CONFIRMED",
    date: nextWeekDates[2], // Wednesday next week
    service: "Men's Haircut",
    teamMember: "Victor Harvley",
    teamMemberId: "3", // Added teamMemberId
  },
  {
    id: "35",
    clientId: "7",
    clientName: "Theo Van Dijk",
    startTime: "13:30",
    endTime: "14:00",
    title: "Theo Van Dijk",
    status: "CONFIRMED",
    date: nextWeekDates[2], // Wednesday next week
    service: "Beard Trim",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
  {
    id: "36",
    clientId: "9",
    clientName: "David Smith",
    startTime: "16:00",
    endTime: "16:30",
    title: "David Smith",
    status: "CONFIRMED",
    date: nextWeekDates[3], // Thursday next week
    service: "Men's Haircut",
    teamMember: "Gwenna Evans",
    teamMemberId: "2", // Added teamMemberId
  },
  {
    id: "37",
    clientId: "2",
    clientName: "Lukas Müller",
    startTime: "10:30",
    endTime: "11:15",
    title: "Lukas Müller",
    status: "CONFIRMED",
    date: nextWeekDates[3], // Thursday next week
    service: "Men's Haircut",
    teamMember: "Arthur Taylor",
    teamMemberId: "1", // Added teamMemberId
  },
];
