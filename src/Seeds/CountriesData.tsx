// Sample data for countries, cities, and regions
const countries = [
  { name: "India", code: "IN" },
  { name: "United States", code: "US" },
  { name: "United Kingdom", code: "UK" },
  { name: "Canada", code: "CA" },
  { name: "Australia", code: "AU" },
  { name: "Germany", code: "DE" },
  { name: "France", code: "FR" },
  { name: "Japan", code: "JP" },
  { name: "Brazil", code: "BR" },
  { name: "South Africa", code: "ZA" },
  { name: "Mexico", code: "MX" },
  { name: "Spain", code: "ES" },
  { name: "Italy", code: "IT" },
  { name: "China", code: "CN" },
  { name: "Russia", code: "RU" },
  { name: "Egypt", code: "EG" },
  { name: "Argentina", code: "AR" },
  { name: "New Zealand", code: "NZ" },
  { name: "Sweden", code: "SE" },
  { name: "Netherlands", code: "NL" },
];

const citiesByCountry: { [key: string]: string[] } = {
  IN: [
    "Delhi",
    "Mumbai",
    "Bangalore",
    "Chennai",
    "Kolkata",
    "Hyderabad",
    "Pune",
    "Ahmedabad",
  ],
  US: [
    "New York",
    "Los Angeles",
    "Chicago",
    "Houston",
    "Philadelphia",
    "Phoenix",
    "San Antonio",
    "San Diego",
    "Dallas",
    "San Jose",
  ],
  UK: [
    "London",
    "Manchester",
    "Birmingham",
    "Glasgow",
    "Liverpool",
    "Edinburgh",
    "Bristol",
    "Leeds",
    "Newcastle upon Tyne",
    "Sheffield",
  ],
  CA: [
    "Toronto",
    "Vancouver",
    "Montreal",
    "Calgary",
    "Ottawa",
    "Edmonton",
    "Quebec City",
    "Hamilton",
    "Winnipeg",
    "Halifax",
  ],
  AU: [
    "Sydney",
    "Melbourne",
    "Brisbane",
    "Perth",
    "Adelaide",
    "Canberra",
    "Gold Coast",
    "Newcastle",
    "Wollongong",
    "Hobart",
  ],
  DE: [
    "Berlin",
    "Hamburg",
    "Munich",
    "Cologne",
    "Frankfurt",
    "Stuttgart",
    "Düsseldorf",
    "Dortmund",
    "Essen",
    "Leipzig",
  ],
  FR: [
    "Paris",
    "Marseille",
    "Lyon",
    "Toulouse",
    "Nice",
    "Nantes",
    "Strasbourg",
    "Bordeaux",
    "Lille",
    "Rennes",
  ],
  JP: [
    "Tokyo",
    "Yokohama",
    "Osaka",
    "Nagoya",
    "Sapporo",
    "Fukuoka",
    "Kawasaki",
    "Kyoto",
    "Saitama",
    "Hiroshima",
  ],
  BR: [
    "São Paulo",
    "Rio de Janeiro",
    "Brasília",
    "Salvador",
    "Fortaleza",
    "Belo Horizonte",
    "Manaus",
    "Curitiba",
    "Recife",
    "Porto Alegre",
  ],
  ZA: [
    "Johannesburg",
    "Cape Town",
    "Durban",
    "Pretoria",
    "Port Elizabeth",
    "Bloemfontein",
    "East London",
    "Pietermaritzburg",
    "Germiston",
    "Soweto",
  ],
  MX: [
    "Mexico City",
    "Guadalajara",
    "Monterrey",
    "Puebla",
    "Tijuana",
    "Leon",
    "Ciudad Juarez",
    "Zapopan",
    "Nezahualcoyotl",
    "Merida",
  ],
  ES: [
    "Madrid",
    "Barcelona",
    "Valencia",
    "Seville",
    "Zaragoza",
    "Malaga",
    "Murcia",
    "Palma de Mallorca",
    "Las Palmas de Gran Canaria",
    "Bilbao",
  ],
  IT: [
    "Rome",
    "Milan",
    "Naples",
    "Turin",
    "Palermo",
    "Genoa",
    "Bologna",
    "Florence",
    "Bari",
    "Catania",
  ],
  CN: [
    "Shanghai",
    "Beijing",
    "Chongqing",
    "Tianjin",
    "Guangzhou",
    "Shenzhen",
    "Chengdu",
    "Hangzhou",
    "Wuhan",
    "Nanjing",
  ],
  RU: [
    "Moscow",
    "Saint Petersburg",
    "Novosibirsk",
    "Yekaterinburg",
    "Kazan",
    "Nizhny Novgorod",
    "Chelyabinsk",
    "Omsk",
    "Samara",
    "Rostov-on-Don",
  ],
  EG: [
    "Cairo",
    "Alexandria",
    "Giza",
    "Shubra El Kheima",
    "Port Said",
    "Suez",
    "Luxor",
    "Mansoura",
    "Tanta",
    "Assiut",
  ],
  AR: [
    "Buenos Aires",
    "Cordoba",
    "Rosario",
    "Mendoza",
    "La Plata",
    "Mar del Plata",
    "San Miguel de Tucuman",
    "Salta",
    "Santa Fe",
    "Corrientes",
  ],
  NZ: [
    "Auckland",
    "Wellington",
    "Christchurch",
    "Hamilton",
    "Napier-Hastings",
    "Tauranga",
    "Dunedin",
    "Palmerston North",
    "Nelson",
    "Rotorua",
  ],
  SE: [
    "Stockholm",
    "Gothenburg",
    "Malmo",
    "Uppsala",
    "Vasteras",
    "Orebro",
    "Linkoping",
    "Helsingborg",
    "Jonkoping",
    "Norrkoping",
  ],
  NL: [
    "Amsterdam",
    "Rotterdam",
    "The Hague",
    "Utrecht",
    "Eindhoven",
    "Groningen",
    "Tilburg",
    "Almere",
    "Breda",
    "Nijmegen",
  ],
};

const regionsByCity: { [key: string]: string[] } = {
  // India
  Delhi: [
    "North Delhi",
    "South Delhi",
    "East Delhi",
    "West Delhi",
    "Central Delhi",
    "New Delhi",
  ],
  Mumbai: [
    "South Mumbai",
    "Andheri",
    "Bandra",
    "Navi Mumbai",
    "Borivali",
    "Kandivali",
    "Ghatkopar",
    "Chembur",
  ],
  Bangalore: [
    "Koramangala",
    "Indiranagar",
    "Whitefield",
    "Jayanagar",
    "HSR Layout",
    "JP Nagar",
    "Malleshwaram",
    "Rajajinagar",
  ],
  Chennai: [
    "T. Nagar",
    "Anna Nagar",
    "Velachery",
    "Adyar",
    "Nungambakkam",
    "Mylapore",
    "Guindy",
    "Ambattur",
  ],
  Kolkata: [
    "North Kolkata",
    "South Kolkata",
    "Central Kolkata",
    "East Kolkata",
    "West Kolkata",
  ],
  Hyderabad: [
    "Gachibowli",
    "Jubilee Hills",
    "Banjara Hills",
    "HITEC City",
    "Secunderabad",
    "Kukatpally",
    "Madhapur",
    "Begumpet",
  ],
  Pune: [
    "Koregaon Park",
    "Kalyani Nagar",
    "Baner",
    "Hinjewadi",
    "Aundh",
    "Pimple Saudagar",
    "Shivaji Nagar",
    "Hadapsar",
  ],
  Ahmedabad: [
    "Navrangpura",
    "Satellite",
    "CG Road",
    "Thaltej",
    "Maninagar",
    "Vastrapur",
    "Bodakdev",
    "Paldi",
  ],
  // United States
  "New York": ["Manhattan", "Brooklyn", "Queens", "Bronx", "Staten Island"],
  "Los Angeles": [
    "Downtown",
    "Hollywood",
    "Santa Monica",
    "Beverly Hills",
    "Pasadena",
    "Long Beach",
    "San Fernando Valley",
    "West Hollywood",
  ],
  Chicago: [
    "Loop",
    "Lincoln Park",
    "Hyde Park",
    "Wicker Park",
    "Lake View",
    "West Town",
    "Near North Side",
    "South Side",
  ],
  Houston: [
    "Downtown",
    "Montrose",
    "Midtown",
    "Galleria",
    "West University",
    "Memorial",
    "Katy",
    "Sugar Land",
  ],
  Philadelphia: [
    "Center City",
    "West Philadelphia",
    "North Philadelphia",
    "South Philadelphia",
    "University City",
  ],
  Phoenix: [
    "Downtown",
    "Midtown",
    "Camelback East",
    "Ahwatukee Foothills",
    "North Phoenix",
  ],
  "San Antonio": [
    "Downtown",
    "Midtown",
    "Alamo Heights",
    "Stone Oak",
    "North San Antonio",
  ],
  "San Diego": [
    "Downtown",
    "La Jolla",
    "Pacific Beach",
    "Gaslamp Quarter",
    "Old Town",
  ],
  Dallas: ["Downtown", "Uptown", "Oak Lawn", "Highland Park", "Preston Hollow"],
  "San Jose": ["Downtown", "Willow Glen", "Alviso", "Berryessa", "Evergreen"],
  // United Kingdom
  London: [
    "Westminster",
    "Camden",
    "Kensington",
    "Shoreditch",
    "Islington",
    "Chelsea",
    "Greenwich",
    "Hackney",
  ],
  Manchester: [
    "City Centre",
    "Northern Quarter",
    "Didsbury",
    "Chorlton",
    "Salford Quays",
    "Spinningfields",
    "Rusholme",
    "Fallowfield",
  ],
  Birmingham: [
    "City Centre",
    "Digbeth",
    "Edgbaston",
    "Selly Oak",
    "Moseley",
    "Jewellery Quarter",
    "Harborne",
    "Kings Heath",
  ],
  Glasgow: [
    "City Centre",
    "West End",
    "Southside",
    "Merchant City",
    "East End",
    "North Glasgow",
  ],
  Liverpool: [
    "City Centre",
    "Anfield",
    "Toxteth",
    "Woolton",
    "Allerton",
    "Kirkdale",
  ],
  Edinburgh: ["Old Town", "New Town", "Leith", "Stockbridge", "Haymarket"],
  Bristol: ["City Centre", "Clifton", "Stokes Croft", "Southville", "Redland"],
  Leeds: [
    "City Centre",
    "Headingley",
    "Roundhay",
    "Kirkstall",
    "Chapel Allerton",
  ],
  "Newcastle upon Tyne": [
    "City Centre",
    "Jesmond",
    "Gosforth",
    "Heaton",
    "Ouseburn",
  ],
  Sheffield: [
    "City Centre",
    "Ecclesall Road",
    "Hillsborough",
    "Broomhill",
    "Kelham Island",
  ],
  // Canada
  Toronto: [
    "Downtown",
    "North York",
    "Scarborough",
    "Etobicoke",
    "Midtown",
    "York",
    "East York",
  ],
  Vancouver: [
    "Downtown",
    "Kitsilano",
    "Yaletown",
    "Mount Pleasant",
    "Gastown",
    "West End",
    "Main Street",
    "Commercial Drive",
  ],
  Montreal: [
    "Old Montreal",
    "Plateau",
    "Mile End",
    "Downtown",
    "Westmount",
    "Outremont",
    "Hochelaga-Maisonneuve",
    "Côte-des-Neiges",
  ],
  Calgary: [
    "Downtown",
    "Beltline",
    "Kensington",
    "Inglewood",
    "Eau Claire",
    "Mission",
    "Bridgeland",
    "Marda Loop",
  ],
  Ottawa: [
    "Downtown",
    "Centretown",
    "The Glebe",
    "Westboro",
    "Sandy Hill",
    "Kanata",
    "Nepean",
  ],
  Edmonton: [
    "Downtown",
    "Old Strathcona",
    "Oliver",
    "Garneau",
    "Whyte Avenue",
    "Westmount",
    "Jasper Avenue",
  ],
  "Quebec City": [
    "Old Quebec",
    "Sainte-Foy",
    "Limoilou",
    "Saint-Jean-Baptiste",
    "Montcalm",
  ],
  Hamilton: ["Downtown", "Westdale", "Ancaster", "Dundas", "Stoney Creek"],
  Winnipeg: [
    "Downtown",
    "Osborne Village",
    "Exchange District",
    "Corydon Village",
    "West End",
  ],
  Halifax: ["Downtown", "North End", "South End", "West End", "Dartmouth"],
  // Australia
  Sydney: [
    "CBD",
    "Inner West",
    "Eastern Suburbs",
    "North Shore",
    "Northern Beaches",
    "Western Sydney",
    "South Sydney",
  ],
  Melbourne: [
    "CBD",
    "Inner North",
    "Inner South",
    "Eastern Suburbs",
    "Western Suburbs",
    "Northern Suburbs",
    "South-Eastern Suburbs",
  ],
  Brisbane: [
    "CBD",
    "Inner City",
    "North Brisbane",
    "South Brisbane",
    "East Brisbane",
    "West Brisbane",
  ],
  Perth: [
    "CBD",
    "Northbridge",
    "Fremantle",
    "Subiaco",
    "Leederville",
    "Cottesloe",
    "Scarborough",
  ],
  Adelaide: [
    "CBD",
    "North Adelaide",
    "South Adelaide",
    "East Adelaide",
    "West Adelaide",
    "Glenelg",
    "Norwood",
  ],
  Canberra: [
    "City Centre",
    "Inner North",
    "Inner South",
    "Woden Valley",
    "Belconnen",
    "Gungahlin",
  ],
  "Gold Coast": [
    "Surfers Paradise",
    "Broadbeach",
    "Southport",
    "Coolangatta",
    "Nerang",
  ],
  Newcastle: ["CBD", "Hamilton", "Merewether", "Kotara", "Warners Bay"],
  Wollongong: [
    "CBD",
    "North Wollongong",
    "Figtree",
    "Corrimal",
    "Shellharbour",
  ],
  Hobart: ["CBD", "North Hobart", "Sandy Bay", "Battery Point", "West Hobart"],
  // Germany
  Berlin: [
    "Mitte",
    "Prenzlauer Berg",
    "Kreuzberg",
    "Friedrichshain",
    "Charlottenburg",
    "Schöneberg",
    "Neukölln",
    "Steglitz",
  ],
  Hamburg: [
    "St. Pauli",
    "Altona",
    "Eimsbüttel",
    "Wandsbek",
    "Harburg",
    "Bergedorf",
    "Mitte",
  ],
  Munich: [
    "Altstadt-Lehel",
    "Schwabing-West",
    "Maxvorstadt",
    "Au-Haidhausen",
    "Sendling-Westpark",
    "Neuhausen-Nymphenburg",
    "Bogenhausen",
  ],
  Cologne: [
    "Innenstadt",
    "Ehrenfeld",
    "Nippes",
    "Lindenthal",
    "Kalk",
    "Mülheim",
    "Chorweiler",
  ],
  Frankfurt: [
    "Innenstadt",
    "Westend",
    "Nordend",
    "Sachsenhausen",
    "Bockenheim",
    "Bornheim",
    "Ostend",
  ],
  Stuttgart: [
    "Stuttgart-Mitte",
    "Bad Cannstatt",
    "Degerloch",
    "Esslingen",
    "Feuerbach",
    "Vaihingen",
    "Zuffenhausen",
  ],
  Düsseldorf: [
    "Stadtmitte",
    "Altstadt",
    "Oberbilk",
    "Unterbilk",
    "Bilk",
    "Heerdt",
    "Oberkassel",
  ],
  Dortmund: [
    "Innenstadt-West",
    "Innenstadt-Ost",
    "Hombruch",
    "Hörde",
    "Mengede",
    "Brackel",
    "Eving",
  ],
  Essen: [
    "Stadtkern",
    "Rüttenscheid",
    "Werden",
    "Kettwig",
    "Borbeck",
    "Altenessen",
    "Steele",
  ],
  Leipzig: [
    "Mitte",
    "Südvorstadt",
    "Plagwitz",
    "Connewitz",
    "Gohlis",
    "Lindenau",
    "Reudnitz-Thonberg",
  ],
  // France
  Paris: [
    "1st arrondissement (Louvre)",
    "2nd arrondissement (Bourse)",
    "3rd arrondissement (Marais)",
    "4th arrondissement (Marais)",
    "5th arrondissement (Latin Quarter)",
    "6th arrondissement (Saint-Germain-des-Prés)",
    "7th arrondissement (Eiffel Tower)",
    "8th arrondissement (Champs-Élysées)",
    "9th arrondissement (Opéra)",
    "10th arrondissement (Gare du Nord)",
  ],
  Marseille: [
    "1st arrondissement",
    "2nd arrondissement",
    "3rd arrondissement",
    "4th arrondissement",
    "5th arrondissement",
    "6th arrondissement",
    "7th arrondissement",
    "8th arrondissement",
    "9th arrondissement",
    "10th arrondissement",
  ],
  Lyon: [
    "1st arrondissement",
    "2nd arrondissement",
    "3rd arrondissement",
    "4th arrondissement (Croix-Rousse)",
    "5th arrondissement (Vieux Lyon)",
    "6th arrondissement",
    "7th arrondissement",
    "8th arrondissement",
    "9th arrondissement",
  ],
  Toulouse: [
    "Capitole",
    "Saint-Étienne",
    "Saint-Cyprien",
    "Les Carmes",
    "Compans-Caffarelli",
    "Rangueil",
    "Mirail-Université",
    "Lalande",
  ],
  Nice: [
    "Vieux Nice (Old Town)",
    "Promenade des Anglais",
    "Cimiez",
    "Liberation",
    "Gare Thiers",
    "Port Lympia",
    "Mont Boron",
  ],
  Nantes: [
    "Centre-ville",
    "Graslin",
    "Bouffay",
    "Canclaux - Mellinet",
    "Zola",
    "Sainte-Thérèse",
    "Bellevue - Chantenay - Sainte-Anne",
  ],
  Strasbourg: [
    "Grande Île",
    "Krutenau",
    "Neustadt (German Quarter)",
    "Robertsau",
    "Meinau",
    "Esplanade",
    "Cronenbourg",
  ],
  Bordeaux: [
    "Centre-ville (Saint-Pierre)",
    "Chartrons",
    "Saint-Michel",
    "Bastide",
    "Pessac-Nouveau",
    "Caudéran",
    "Les Chartrons",
  ],
  Lille: [
    "Vieux Lille (Old Town)",
    "Centre",
    "Wazemmes",
    "Esquermes",
    "Vauban-Esquermes",
    "Fives",
    "Moulins",
  ],
  Rennes: [
    "Centre",
    "Thabor - Saint-Hélier",
    "Villejean - Beauregard",
    "Cleunay - Arsenal-Redon",
    "Le Blosne",
    "Bréquigny",
  ],
  // Japan
  Tokyo: [
    "Shinjuku",
    "Shibuya",
    "Ginza",
    "Asakusa",
    "Ueno",
    "Roppongi",
    "Akihabara",
    "Ikebukuro",
    "Marunouchi",
    "Odaiba",
  ],
  Yokohama: [
    "Minato Mirai 21",
    "Chinatown",
    "Motomachi",
    "Kannai",
    "Shin-Yokohama",
    "Sankeien Garden",
    "Yamashita Park",
  ],
  Osaka: [
    "Umeda",
    "Namba",
    "Shinsaibashi",
    "Dotonbori",
    "Tennoji",
    "Shinsekai",
    "Osaka Castle Park",
  ],
  Nagoya: [
    "Sakae",
    "Nagoya Station Area",
    "Osu",
    "Kanayama",
    "Arimatsu",
    "Togokusan Fruit Park",
  ],
  Sapporo: [
    "Odori Park",
    "Susukino",
    "Sapporo Station Area",
    "Shiroi Koibito Park",
    "Maruyama Zoo",
  ],
  Fukuoka: [
    "Hakata",
    "Tenjin",
    "Nakasu",
    "Dazaifu",
    "Canal City Hakata",
    "Ohori Park",
  ],
  Kawasaki: [
    "Kawasaki Station Area",
    "Musashi-Kosugi",
    "Lafole",
    "Kawasaki Daishi Temple",
  ],
  Kyoto: [
    "Gion",
    "Kiyomizu-dera Temple Area",
    "Arashiyama",
    "Fushimi Inari Shrine Area",
    "Kinkaku-ji (Golden Pavilion) Area",
  ],
  Saitama: ["Omiya", "Urawa", "Shintoshin", "Kawagoe", "Tokorozawa"],
  Hiroshima: [
    "Hiroshima Peace Memorial Park",
    "Hondori Shopping Street",
    "Miyajima Island",
    "Hiroshima Castle",
    "Shukkei-en Garden",
  ],
  // Brazil
  "São Paulo": [
    "Centro",
    "Pinheiros",
    "Vila Madalena",
    "Itaim Bibi",
    "Jardins",
    "Moema",
    "Liberdade",
    "Paulista Avenue",
  ],
  "Rio de Janeiro": [
    "Copacabana",
    "Ipanema",
    "Leblon",
    "Santa Teresa",
    "Lapa",
    "Barra da Tijuca",
    "Centro",
  ],
  Brasília: [
    "Plano Piloto (Asa Sul, Asa Norte)",
    "Lago Sul",
    "Lago Norte",
    "Águas Claras",
    "Taguatinga",
  ],
  Salvador: [
    "Pelourinho",
    "Barra",
    "Rio Vermelho",
    "Ondina",
    "Itapuã",
    "Pituba",
  ],
  Fortaleza: [
    "Praia de Iracema",
    "Beira Mar",
    "Aldeota",
    "Varjota",
    "Meireles",
    "Praia do Futuro",
  ],
  "Belo Horizonte": [
    "Savassi",
    "Funcionários",
    "Lourdes",
    "Cidade Jardim",
    "Pampulha",
    "Santa Tereza",
  ],
  Manaus: ["Centro", "Adrianópolis", "Vieiralves", "Ponta Negra", "Chapada"],
  Curitiba: [
    "Centro",
    "Batel",
    "Bigorrilho",
    "Água Verde",
    "Santa Felicidade",
    "Boqueirão",
  ],
  Recife: [
    "Boa Viagem",
    "Recife Antigo",
    "Boa Vista",
    "Casa Forte",
    "Graças",
    "Pina",
  ],
  "Porto Alegre": [
    "Centro Histórico",
    "Moinhos de Vento",
    "Bom Fim",
    "Cidade Baixa",
    "Petrópolis",
    "Tristeza",
  ],
  // South Africa
  Johannesburg: [
    "Sandton",
    "Rosebank",
    "Melville",
    "Braamfontein",
    "Soweto",
    "Maboneng Precinct",
    "Parktown",
  ],
  "Cape Town": [
    "City Bowl",
    "Waterfront",
    "Camps Bay",
    "Clifton",
    "Sea Point",
    "Woodstock",
    "Bo-Kaap",
  ],
  Durban: [
    "North Beach",
    "South Beach",
    "Umhlanga",
    "Morningside",
    "Florida Road",
    "Glenwood",
  ],
  Pretoria: [
    "Central Business District (CBD)",
    "Arcadia",
    "Hatfield",
    "Menlyn",
    "Brooklyn",
    "Waterkloof",
  ],
  "Port Elizabeth": [
    "Summerstrand",
    "Humewood",
    "Central",
    "Walmer",
    "Newton Park",
  ],
  Bloemfontein: [
    "Central",
    "Westdene",
    "Brandwag",
    "Universitas",
    "Kloof",
    "Kromdraai",
  ],
  "East London": [
    "City Centre",
    "Kingsway",
    "Kingswood",
    "Kingsburgh",
    "Kingsmead",
  ],
  Pietermaritzburg: [
    "City Centre",
    "Durban Road",
    "Kingsway",
    "Kingswood",
    "Kingsmead",
  ],
  Germiston: [
    "City Centre",
    "Randpark Ridge",
    "Kingsway",
    "Kingswood",
    "Kingsmead",
  ],
  Soweto: ["Maboneng", "Mamelodi", "Mogale City", "Midrand", "Randburg"],
  // Mexico
  "Mexico City": [
    "Roma Norte",
    "Condesa",
    "Polanco",
    "Centro Histórico",
    "Coyoacán",
    "Reforma",
    "Narvarte",
    "San Ángel",
  ],
  Guadalajara: [
    "Chapultepec",
    "Providencia",
    "Americana",
    "Centro",
    "Zapopan Centro",
    "Tlaquepaque",
  ],
  Monterrey: [
    "San Pedro Garza García",
    "Centro",
    "Valle Oriente",
    "Cumbres",
    "Contry",
  ],
  Puebla: ["Centro Histórico", "Angelópolis", "Cholula", "La Paz", "Zavaleta"],
  Tijuana: [
    "Zona Río",
    "Playas de Tijuana",
    "Centro",
    "Otay Centenario",
    "La Mesa",
  ],
  Leon: [
    "Centro",
    "Zona Dorada",
    "Campestre",
    "Jardines del Moral",
    "La Martinica",
  ],
  "Ciudad Juarez": [
    "Centro",
    "Las Torres",
    "Golden Zone",
    "El Paso del Norte",
    "Aldama",
  ],
  Zapopan: [
    "Andares",
    "Puerta de Hierro",
    "Centro",
    "Valle Real",
    "Avenida Patria",
  ],
  Nezahualcoyotl: [
    "Benito Juárez",
    "Ciudad Jardín",
    "El Sol",
    "La Perla",
    "Valle de Aragón",
  ],
  Merida: [
    "Centro",
    "Paseo de Montejo",
    "Colonia México",
    "Altabrisa",
    "Norte",
  ],
  // Spain
  Madrid: [
    "Centro",
    "Salamanca",
    "Chamberí",
    "Malasaña",
    "La Latina",
    "Retiro",
    "Argüelles",
    "Chueca",
  ],
  Barcelona: [
    "Gothic Quarter",
    "Eixample",
    "Gràcia",
    "El Born",
    "Barceloneta",
    "Poble Sec",
    "Sant Antoni",
    "Poblenou",
  ],
  Valencia: [
    "Ciutat Vella",
    "Eixample",
    "Ruzafa",
    "El Carmen",
    "Benimaclet",
    "Algirós",
    "Poblats Marítims",
  ],
  Seville: [
    "Triana",
    "Macarena",
    "Nervión",
    "Santa Cruz",
    "Los Remedios",
    "Casco Antiguo",
  ],
  Zaragoza: ["Casco Histórico", "Actur", "Delicias", "El Portillo", "Romareda"],
  Malaga: [
    "Centro Histórico",
    "Pedregalejo",
    "Malagueta",
    "Teatinos",
    "El Limonar",
  ],
  Murcia: ["Centro", "El Carmen", "Vista Alegre", "La Flota", "Espinardo"],
  "Palma de Mallorca": [
    "Casco Antiguo",
    "Santa Catalina",
    "El Molinar",
    "Portixol",
    "La Lonja",
  ],
  "Las Palmas de Gran Canaria": [
    "Vegueta",
    "Triana",
    "Las Canteras",
    "Mesa y López",
    "Isleta",
  ],
  Bilbao: ["Casco Viejo", "Indautxu", "Abandoibarra", "Deusto", "Ensanche"],
  // Italy
  Rome: [
    "Trastevere",
    "Monti",
    "Prati",
    "Testaccio",
    "Garbatella",
    "San Lorenzo",
    "EUR",
    "Flaminio",
  ],
  Milan: [
    "Brera",
    "Navigli",
    "Duomo",
    "Porta Nuova",
    "Isola",
    "Magenta",
    "Ticinese",
    "Corso Buenos Aires",
  ],
  Naples: [
    "Centro Storico",
    "Vomero",
    "Chiaia",
    "Posillipo",
    "Spaccanapoli",
    "Mergellina",
  ],
  Turin: [
    "Centro",
    "San Salvario",
    "Quadrilatero Romano",
    "Crocetta",
    "Vanchiglia",
    "Borgo Po",
  ],
  Palermo: [
    "Centro Storico",
    "Kalsa",
    "La Vucciria",
    "Monte di Pietà",
    "Politeama",
  ],
  Genoa: ["Centro Storico", "Boccadasse", "Nervi", "Sampierdarena", "Pegli"],
  Bologna: [
    "Centro Storico",
    "Saragozza",
    "Santo Stefano",
    "Porta Saragozza",
    "San Vitale",
  ],
  Florence: [
    "Duomo",
    "Oltrarno",
    "San Niccolò",
    "Santo Spirito",
    "Santa Croce",
  ],
  Bari: ["Bari Vecchia", "Murat", "Libertà", "Carrassi", "Poggiofranco"],
  Catania: [
    "Centro Storico",
    "San Berillo",
    "Barriera",
    "Borgo-Sanzio",
    "Nesima",
  ],
  // China
  Shanghai: [
    "Pudong",
    "Puxi",
    "Jing'an",
    "Huangpu",
    "Xuhui",
    "Minhang",
    "Hongkou",
    "Changning",
  ],
  Beijing: [
    "Chaoyang",
    "Haidian",
    "Dongcheng",
    "Xicheng",
    "Fengtai",
    "Shunyi",
    "Tongzhou",
    "Fangshan",
  ],
  Chongqing: [
    "Yuzhong",
    "Jiangbei",
    "Nan'an",
    "Shapingba",
    "Dadukou",
    "Jiulongpo",
    "Beibei",
    "Yubei",
  ],
  Tianjin: [
    "Heping",
    "Nankai",
    "Hedong",
    "Hexi",
    "Hongqiao",
    "Hebei",
    "Binhai New Area",
  ],
  Guangzhou: [
    "Tianhe",
    "Yuexiu",
    "Haizhu",
    "Liwan",
    "Baiyun",
    "Panyu",
    "Huadu",
    "Nansha",
  ],
  Shenzhen: [
    "Futian",
    "Nanshan",
    "Luohu",
    "Bao'an",
    "Longgang",
    "Yantian",
    "Guangming",
    "Pingshan",
  ],
  Chengdu: [
    "Jinjiang",
    "Qingyang",
    "Jinniu",
    "Wuhou",
    "Chenghua",
    "Longquanyi",
    "Pidu",
    "Wenjiang",
  ],
  Hangzhou: [
    "Xihu",
    "Gongshu",
    "Shangcheng",
    "Xiacheng",
    "Jianggan",
    "Binjiang",
    "Xiaoshan",
    "Yuhang",
  ],
  Wuhan: [
    "Wuchang",
    "Hankou",
    "Hanyang",
    "Qiaokou",
    "Jianghan",
    "Hongshan",
    "Dongxihu",
    "Caidian",
  ],
  Nanjing: [
    "Gulou",
    "Xuanwu",
    "Qinhuai",
    "Jianye",
    "Yuhuatai",
    "Qixia",
    "Pukou",
    "Jiangning",
  ],
  // Russia
  Moscow: [
    "Central Administrative Okrug",
    "Northern Administrative Okrug",
    "Southern Administrative Okrug",
    "Eastern Administrative Okrug",
    "Western Administrative Okrug",
    "North-Eastern Administrative Okrug",
    "North-Western Administrative Okrug",
    "South-Western Administrative Okrug",
  ],
  "Saint Petersburg": [
    "Central District",
    "Admiralteysky District",
    "Vasileostrovsky District",
    "Petrogradsky District",
    "Nevsky District",
    "Primorsky District",
    "Kalininsky District",
  ],
  Novosibirsk: [
    "Zheleznodorozhny District",
    "Tsentralny District",
    "Zayeltsovsky District",
    "Leninsky District",
    "Kirovsky District",
    "Oktyabrsky District",
  ],
  Yekaterinburg: [
    "Leninsky District",
    "Verkh-Isetsky District",
    "Oktyabrsky District",
    "Kirovsky District",
    "Chkalovsky District",
    "Ordzhonikidzevsky District",
  ],
  Kazan: [
    "Vakhitovsky District",
    "Privolzhsky District",
    "Aviastroitelny District",
    "Kirovsky District",
    "Moskovsky District",
    "Novosavinovsky District",
  ],
  "Nizhny Novgorod": [
    "Nizhny Novgorod District",
    "Soviet District",
    "Prioksky District",
    "Leninsky District",
    "Avtozavodsky District",
    "Kanavinsky District",
  ],
  Chelyabinsk: [
    "Tsentralny District",
    "Kalininsky District",
    "Metallurgichesky District",
    "Leninsky District",
    "Sovetsky District",
    "Traktorozavodsky District",
  ],
  Omsk: [
    "Kirovsky District",
    "Oktryabrsky District",
    "Sovetsky District",
    "Tsentralny District",
    "Leninsky District",
    "Oktyabrsky District",
  ],
  Samara: [
    "Samarsky District",
    "Leninsky District",
    "Zheleznodorozhny District",
    "Sovetsky District",
    "Promyshlenny District",
    "Kirovsky District",
  ],
  "Rostov-on-Don": [
    "Kirovsky District",
    "Leninsky District",
    "Pervomaysky District",
    "Proletarsky District",
    "Sovetsky District",
    "Voroshilovsky District",
  ],
  // Egypt
  Cairo: [
    "Zamalek",
    "Maadi",
    "Heliopolis",
    "Nasr City",
    "Downtown Cairo",
    "New Cairo",
    "Sheikh Zayed City",
    "6th of October City",
  ],
  Alexandria: [
    "Montaza",
    "Sidi Gaber",
    "Raml Station",
    "Smouha",
    "Miami",
    "Kafr Abdou",
    "Agami",
  ],
  Giza: [
    "Dokki",
    "Mohandessin",
    "Haram",
    "Faisal",
    "Imbaba",
    "Zayed",
    "6th of October City",
  ],
  "Shubra El Kheima": ["Shubra El Kheima East", "Shubra El Kheima West"],
  "Port Said": [
    "East Port Said",
    "West Port Said",
    "Arab District",
    "El Manakh",
  ],
  Suez: ["Arbaeen", "Suez District", "El Ganayn", "Faisal"],
  Luxor: ["East Bank", "West Bank", "Karnak", "Downtown Luxor"],
  Mansoura: ["East Mansoura", "West Mansoura", "Talkha"],
  Tanta: ["First Tanta", "Second Tanta"],
  Assiut: ["First Assiut", "Second Assiut", "New Assiut"],
  // Argentina
  "Buenos Aires": [
    "Palermo",
    "Recoleta",
    "San Telmo",
    "Puerto Madero",
    "Belgrano",
    "Caballito",
    "Villa Crespo",
    "Colegiales",
  ],
  Cordoba: [
    "Nueva Córdoba",
    "Cerro de las Rosas",
    "General Paz",
    "Alberdi",
    "Centro",
  ],
  Rosario: ["Pichincha", "Echesortu", "Fisherton", "Abasto", "Centro"],
  Mendoza: [
    "Centro",
    "Chacras de Coria",
    "Godoy Cruz",
    "Las Heras",
    "Guaymallén",
  ],
  "La Plata": ["Casco Urbano", "City Bell", "Gonnet", "Ringuelet", "Tolosa"],
  "Mar del Plata": [
    "Centro",
    "Playa Grande",
    "La Perla",
    "Chauvin",
    "Los Troncos",
  ],
  "San Miguel de Tucuman": [
    "Centro",
    "Yerba Buena",
    "Tafí Viejo",
    "Concepción",
  ],
  Salta: ["Centro", "Tres Cerritos", "Grand Bourg", "Castellanos"],
  "Santa Fe": ["Centro", "Candioti", "Sunchales", "Santo Tomé"],
  Corrientes: ["Centro", "Costanera", "Libertad", "Ex Aeropuerto"],
  // New Zealand
  Auckland: [
    "Central Business District",
    "Ponsonby",
    "Parnell",
    "Newmarket",
    "Takapuna",
    "Remuera",
    "Mission Bay",
    "Mount Eden",
  ],
  Wellington: [
    "Lambton Quay",
    "Cuba Street",
    "Te Aro",
    "Thorndon",
    "Mount Victoria",
    "Miramar",
    "Island Bay",
  ],
  Christchurch: [
    "Central City",
    "Riccarton",
    "Merivale",
    "Fendalton",
    "Halswell",
    "Sumner",
    "Lyttelton",
  ],
  "Napier-Hastings": [
    "Napier City Centre",
    "Hastings City Centre",
    "Ahuriri",
    "Taradale",
    "Clive",
  ],
  Tauranga: [
    "Tauranga Central",
    "Mount Maunganui",
    "Papamoa",
    "Bethlehem",
    "Gate Pa",
  ],
  Dunedin: [
    "Central Dunedin",
    "North Dunedin",
    "South Dunedin",
    "Mornington",
    "Roslyn",
  ],
  "Palmerston North": [
    "Central",
    "Awapuni",
    "Milson",
    "Highbury",
    "Fitzherbert",
  ],
  Nelson: ["Nelson City Centre", "Stoke", "Richmond", "Tahunanui"],
  Rotorua: [
    "Fenton Street",
    "Ngongotaha",
    "Lynmore",
    "Kawaha Point",
    "Tihiotonga",
  ],
  // Sweden
  Stockholm: [
    "Gamla Stan",
    "Södermalm",
    "Norrmalm",
    "Östermalm",
    "Kungsholmen",
    "Vasastan",
    "Djurgården",
    "Bromma",
  ],
  Gothenburg: [
    "Innerstaden",
    "Haga",
    "Majorna",
    "Linnéstaden",
    "Centrum",
    "Vasastaden",
    "Kvillebäcken",
  ],
  Malmo: [
    "Gamla Väster",
    "Södra Innerstaden",
    "Västra Hamnen",
    "Limhamn",
    "Hyllie",
    "Rosengård",
  ],
  Uppsala: ["Centrum", "Fålhagen", "Luthagen", "Kåbo", "Eriksberg"],
  Vasteras: ["Centrum", "Djurgården", "Hammarby", "Gideonsberg", "Rönnby"],
  Orebro: ["Centrum", "Sörbyängen", "Vivalla", "Oxbacken", "Norr"],
  Linkoping: ["Innerstaden", "Vasastaden", "Malmslätt", "Lambohov", "Ryd"],
  Helsingborg: ["Centrum", "Söder", "Norr", "Mariastaden", "Råå"],
  Jonkoping: ["Centrum", "Öster", "Väster", "Ekhagen", "Gränna"],
  Norrkoping: ["Centrum", "Saltängen", "Hageby", "Klockaretorpet", "Eneby"],
  // Netherlands
  Amsterdam: [
    "Centrum",
    "Jordaan",
    "De Pijp",
    "Oud-West",
    "Oud-Zuid",
    "Noord",
    "Oost",
    "West",
  ],
  Rotterdam: [
    "Centrum",
    "Kralingen",
    "Delfshaven",
    "Noord",
    "Hillegersberg-Schiebroek",
    "Feijenoord",
    "Charlois",
  ],
  "The Hague": [
    "Centrum",
    "Scheveningen",
    "Escamp",
    "Segbroek",
    "Loosduinen",
    "Haagse Hout",
  ],
  Utrecht: [
    "Binnenstad",
    "Oost",
    "West",
    "Noordwest",
    "Zuid",
    "Leidsche Rijn",
    "Vleuten-De Meern",
  ],
  Eindhoven: ["Centrum", "Stratum", "Woensel-Zuid", "Tongelre", "Meerhoven"],
  Groningen: [
    "Binnenstad",
    "Korrewegwijk",
    "Oosterpoort",
    "Helpman",
    "Paddepoel",
  ],
  Tilburg: ["Centrum", "Oud-Noord", "Reeshof", "Groenewoud", "Goirle"],
  Almere: ["Stad", "Buiten", "Haven", "Hout", "Poort", "Muziekwijk"],
  Breda: ["Centrum", "Ginneken", "Princenhage", "Haagse Beemden", "Zuid"],
  Nijmegen: [
    "Nijmegen-Oost",
    "Nijmegen-West",
    "Nijmegen-Zuid",
    "Nijmegen-Midden",
    "Lent",
  ],
};

export { countries, citiesByCountry, regionsByCity };
