import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import { RootState } from "../store"; // Assuming RootState is correctly defined
import { sampleServices } from "../sampleData";

// Define the type for a team member (if needed, but not directly used in this slice logic for now)
interface TeamMember {
  id: string;
  name: string;
  email: string;
  checked: boolean;
}

// Define the type for a service
export interface Service {
  id: string;
  businessName: string;
  category: string;
  description: string;
  duration: string;
  customMinutes: string;
  isCustomDuration: boolean;
  priceType: string;
  currency: string;
  price: number;
  teamMembers: any; // Consider defining a more specific type if possible
}

// Define the structure for a category item that DraggableFlatList expects
// This is the shape that `selectServicesByCategory` will return and `reorderCategories` will expect
export interface ServiceCategoryItem {
  category: string; // The category name (e.g., "Haircuts")
  services: Service[]; // The services belonging to this category
}

// --- IMPORTANT: This function will now be used ONCE to set the initial order ---
const getInitialCategoryOrder = (services: Service[]): string[] => {
  // Get unique categories and ensure a stable initial order if needed (e.g., alphabetically)
  const uniqueCategories = Array.from(
    new Set(services.map((service) => service.category))
  );
  // You might want to sort them here initially, e.g., uniqueCategories.sort()
  return uniqueCategories;
};

// Define the initial state
interface ServicesState {
  services: Service[];
  // The 'categories' array will now store the ORDER of category names.
  // The actual category-service mapping is derived.
  categoriesOrder: string[]; // <-- Renamed to clearly indicate it holds the order
}

const initialState: ServicesState = {
  services: sampleServices,
  categoriesOrder: getInitialCategoryOrder(sampleServices), // Initialize with the desired order
};

// Create the slice
const servicesSlice = createSlice({
  name: "services",
  initialState,
  reducers: {
    addService: (state, action: PayloadAction<Omit<Service, "id">>) => {
      const newService: Service = {
        ...action.payload,
        id: `${Date.now()}`,
      };
      state.services.push(newService);
      // Add category to categoriesOrder if not already present
      if (!state.categoriesOrder.includes(newService.category)) {
        state.categoriesOrder.push(newService.category);
      }
    },
    updateService: (state, action: PayloadAction<Service>) => {
      const index = state.services.findIndex(
        (service) => service.id === action.payload.id
      );
      if (index !== -1) {
        state.services[index] = action.payload;
        // Add category to categoriesOrder if not already present
        if (!state.categoriesOrder.includes(action.payload.category)) {
          state.categoriesOrder.push(action.payload.category);
        }
      }
    },
    deleteService: (state, action: PayloadAction<string>) => {
      const serviceToDelete = state.services.find(
        (s) => s.id === action.payload
      );
      state.services = state.services.filter(
        (service) => service.id !== action.payload
      );
      // Optionally, if a category becomes empty after deletion, remove it from categoriesOrder
      if (serviceToDelete) {
        const categoryServicesCount = state.services.filter(
          (s) => s.category === serviceToDelete.category
        ).length;
        if (categoryServicesCount === 0) {
          state.categoriesOrder = state.categoriesOrder.filter(
            (cat) => cat !== serviceToDelete.category
          );
        }
      }
    },
    // --- NEW REDUCER FOR REORDERING CATEGORIES ---
    reorderCategories: (
      state,
      action: PayloadAction<ServiceCategoryItem[]>
    ) => {
      // The payload from DraggableFlatList is an array of the *rendered* items.
      // We only care about the 'category' property to update our categoriesOrder.
      state.categoriesOrder = action.payload.map((item) => item.category);
    },
    // --- END NEW REDUCER ---
  },
});

// Export actions
export const { addService, updateService, deleteService, reorderCategories } =
  servicesSlice.actions; // <-- Export the new action

// Export selectors
export const selectServices = (state: RootState) => state.services.services;
// export const selectCategories = (state: RootState) => state.services.categories; // <-- This is replaced by categoriesOrder now

// Memoized selector for services by category, now respecting categoriesOrder
export const selectServicesByCategory = createSelector(
  [selectServices, (state: RootState) => state.services.categoriesOrder], // Listen to both services and their order
  (services, categoriesOrder) => {
    const categoryMap: { [key: string]: Service[] } = {};
    services.forEach((service) => {
      if (!categoryMap[service.category]) {
        categoryMap[service.category] = [];
      }
      categoryMap[service.category].push(service);
    });

    // Construct the result based on the categoriesOrder, ensuring all categories are present
    // even if they temporarily have no services (though filter will remove them later).
    return categoriesOrder
      .map((categoryName) => ({
        category: categoryName,
        services: categoryMap[categoryName] || [], // Ensure services array exists
      }))
      .filter((categoryItem) => categoryItem.services.length > 0); // Only return categories that have services
  }
);

// Export reducer
export default servicesSlice.reducer;
