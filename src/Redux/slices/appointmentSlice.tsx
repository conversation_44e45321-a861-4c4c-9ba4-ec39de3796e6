import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { appointments as initialAppointments } from '../../Seeds/AppointmentSeeds';

// Define the type for an appointment
export interface Appointment {
  id: string;
  clientId: string;
  clientName: string;
  clientPhone?: string;
  startTime: string;
  endTime: string;
  title: string;
  status: string;
  date: string;
  service: string;
  teamMember: string;
  teamMemberId?: string;
}

// Define the initial state
interface AppointmentState {
  appointments: Appointment[];
}

const initialState: AppointmentState = {
  appointments: initialAppointments,
};

// Create the slice
const appointmentSlice = createSlice({
  name: 'appointments',
  initialState,
  reducers: {
    addAppointment: (state, action: PayloadAction<Omit<Appointment, 'id'>>) => {
      const newAppointment: Appointment = {
        ...action.payload,
        id: `${Date.now()}`,
      };
      state.appointments.push(newAppointment);
    },
    updateAppointment: (state, action: PayloadAction<Appointment>) => {
      const index = state.appointments.findIndex(
        (appointment) => appointment.id === action.payload.id
      );
      if (index !== -1) {
        state.appointments[index] = action.payload;
      }
    },
    deleteAppointment: (state, action: PayloadAction<string>) => {
      state.appointments = state.appointments.filter(
        (appointment) => appointment.id !== action.payload
      );
    },
    setAppointments: (state, action: PayloadAction<Appointment[]>) => {
      state.appointments = action.payload;
    },
  },
});

// Export actions
export const {
  addAppointment,
  updateAppointment,
  deleteAppointment,
  setAppointments,
} = appointmentSlice.actions;

// Export reducer
export default appointmentSlice.reducer;
