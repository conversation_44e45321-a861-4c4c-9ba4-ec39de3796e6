import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

export const TeamMebersData = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    number: '01 2345 6789',
    countryCode: 'GB', // GB for United Kingdom
    callingCode: '+44',
    image:
      'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    checked: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    number: '01 2345 6789',
    countryCode: 'GB', // GB for United Kingdom
    callingCode: '+44',
    email: '<EMAIL>',
    image:
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1528&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    checked: false,
  },
  {
    id: '4',
    name: 'Matthew Johnson',
    number: '01 2345 6789',
    countryCode: 'GB', // GB for United Kingdom
    callingCode: '+44',
    email: '<EMAIL>',
    image:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D ',
    checked: false,
  },
  {
    id: '3',
    name: 'Emma Wright',
    email: '<EMAIL>',
    number: '01 2345 6789',
    countryCode: 'GB', // GB for United Kingdom
    callingCode: '+44',
    image:
      'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    checked: false,
  },
];
// Define a type for the slice state
interface MemberSlice {
  members: any[];
}

// Define the initial state using that type
const initialState: MemberSlice = {
  members: TeamMebersData,
};

export const memberSlice = createSlice({
  name: 'members',
  initialState,
  reducers: {
    setMembers: (state, action: PayloadAction<any[]>) => {
      state.members = action.payload;
    },
    setNewMembers: (state, action: PayloadAction<any>) => {
      state.members.push(action.payload);
    },
    deleteMembers: (state, action: PayloadAction<string[]>) => {
      state.members = state.members.filter(
        (client) => !action.payload.includes(client.id)
      );
    },
    updateMember: (state, action: PayloadAction<any>) => {
      const updatedMember = action.payload;
      const index = state.members.findIndex(
        (member) => member.id === updatedMember.id
      );
      if (index !== -1) {
        state.members[index] = updatedMember; // Replace the member with the updated one
      }
    },
  },
});

export const { setMembers, setNewMembers, deleteMembers, updateMember } =
  memberSlice.actions;

export default memberSlice.reducer;
