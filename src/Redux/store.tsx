import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import modalReducer from "./slices/modalSlice";
import initialReducer from "./slices/initialSlice";
import clientsReducer from "./slices/clientSlice";
import memberssReducer from "./slices/memberSlice";
import servicesReducer, { addService } from './slices/servicesSlice';
import categoryReducer from './slices/categorySlice';
import packagesReducer, { addPackage } from './slices/packagesSlice';
import appointmentReducer from "./slices/appointmentSlice";
import businessReducer from "./slices/businessSlice";
import userReducer from "./slices/userSlice";

// Create the store
export const store = configureStore({
  reducer: {
    modals: modalReducer,
    initial: initialReducer,
    clients: clientsReducer,
    members: memberssReducer,
    services: servicesReducer,
    categories: categoryReducer,
    packages: packagesReducer,
    appointments: appointmentReducer,
    business: businessReducer,
    user: userReducer,
  },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();

