import { Service } from './slices/servicesSlice';
import { Package } from './slices/packagesSlice';

// Sample services
export const sampleServices: Service[] = [
  {
    id: '1',
    businessName: 'Classic Haircut',
    category: 'Hair',
    description: 'A traditional haircut with scissors and clippers.',
    duration: '30 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Fixed price',
    currency: 'EUR',
    price: 25,
    teamMembers: [],
  },
  {
    id: '2',
    businessName: 'Beard Trim',
    category: 'Beard & Shaving',
    description: 'Professional beard trimming and shaping.',
    duration: '15 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Fixed price',
    currency: 'EUR',
    price: 15,
    teamMembers: [],
  },
  {
    id: '3',
    businessName: 'Facial Treatment',
    category: 'Face treatment',
    description: 'Rejuvenating facial treatment for all skin types.',
    duration: '45 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Fixed price',
    currency: 'EUR',
    price: 40,
    teamMembers: [],
  },
  {
    id: '4',
    businessName: 'Hair Coloring',
    category: 'Hair',
    description: 'Professional hair coloring service.',
    duration: '60 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Fixed price',
    currency: 'EUR',
    price: 50,
    teamMembers: [],
  },
  {
    id: '5',
    businessName: 'Hot Towel Shave',
    category: 'Beard & Shaving',
    description: 'Traditional hot towel shave with straight razor.',
    duration: '30 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Fixed price',
    currency: 'EUR',
    price: 35,
    teamMembers: [],
  },
];

// Sample packages
export const samplePackages: Package[] = [
  {
    id: '1',
    packageName: 'Gentleman\'s Package',
    category: 'Grooming',
    description: 'Complete grooming package for the modern gentleman.',
    duration: '75 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Aggregate price',
    currency: 'EUR',
    price: 65,
    services: [
      sampleServices[0], // Classic Haircut
      sampleServices[1], // Beard Trim
    ],
  },
  {
    id: '2',
    packageName: 'Deluxe Treatment',
    category: 'Face treatment',
    description: 'Luxury facial and hair treatment package.',
    duration: '105 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Aggregate price',
    currency: 'EUR',
    price: 85,
    services: [
      sampleServices[0], // Classic Haircut
      sampleServices[2], // Facial Treatment
    ],
  },
  {
    id: '3',
    packageName: 'Complete Makeover',
    category: 'Hair',
    description: 'Full makeover including haircut, coloring, and styling.',
    duration: '120 min',
    customMinutes: '',
    isCustomDuration: false,
    priceType: 'Aggregate price',
    currency: 'USD',
    price: 110,
    services: [
      sampleServices[0], // Classic Haircut
      sampleServices[3], // Hair Coloring
    ],
  },
];
