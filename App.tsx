import React from "react";
import { LogBox } from "react-native";
import FlashMessage from "react-native-flash-message";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { SafeAreaProvider } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import { Provider } from "react-redux";
import NetworkLogger from "./src/Components/NetworkLogger";
import ThemedStatusBar from "./src/Components/ThemedStatusBar";
import Routes from "./src/Navigation/Routes";
import { store } from "./src/Redux/store";
import fontFamily from "./src/Utilities/Styles/fontFamily";
import {
  moderateScale,
  textScale,
} from "./src/Utilities/Styles/responsiveSize";
import { ThemeProvider } from "./src/Utilities/ThemeContext";

LogBox.ignoreAllLogs();

export default function App() {
  return (
    <>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Provider store={store}>
          <ThemeProvider>
            <ThemedStatusBar />
            <SafeAreaProvider>
              <Routes />
              <FlashMessage
                titleStyle={{
                  marginRight: moderateScale(5),
                  fontFamily: fontFamily.regular,
                  fontSize: textScale(12),
                }}
                position="top"
              />
              {__DEV__ && <NetworkLogger />}
            </SafeAreaProvider>
          </ThemeProvider>
        </Provider>
      </GestureHandlerRootView>
      <Toast autoHide />
    </>
  );
}
